import React, { useEffect, useState } from "react";
import { Switch, List, Typography, Flex, Card, Divider, theme, message, Spin, Tooltip, Input, Pagination } from "antd";
import { DeleteOutlined } from "@ant-design/icons";
import { blackUrlClientPage, delBlackUrl, settingInfo, editSetting, addClient } from "@/api/knowdge";
import "./index.less";
import EmptyData from "@/components/EmptyData";
const { Text } = Typography;
const { useToken } = theme;
const Knowledge = () => {
  // 从上下文中获取token
  const { token } = useToken();
  const [listLoading, setListLoading] = useState(false);
  const [info, setInfo] = useState<any>({ defaultStorage: false });
  const [inputValue, setInputValue] = useState("");
  const [searchParams, setSearchParams] = useState({
    pageNum: 1,
    pageSize: 10,
    entity: {
      id: "",
      domain: "",
    },
  });

  const [urlList, setUrlList] = useState({
    total: 0,
    current: 1,
    size: 10,
    pages: 0,
    records: [],
  });

  // 获取列表
  const getWordList = () => {
    setListLoading(true);
    blackUrlClientPage(searchParams).then((res) => {
      setListLoading(false);
      if (res.code == 200) {
        setUrlList(res.data);
      }
    });
  };

  // 删除隐藏的网站
  const delWebsite = (id: string) => {
    setListLoading(true);
    delBlackUrl({ id: id }).then((res) => {
      setListLoading(false);
      if (res.code == 200) {
        message.open({
          type: "success",
          content: "删除成功！",
        });
        getWordList();
      }
    });
  };

  // 开关
  const switchChange = (checked: boolean) => {
    setListLoading(true);
    editSetting({ defaultStorage: checked }).then((res: any) => {
      setListLoading(false);
      if (res.code == 200) {
        message.open({
          type: "success",
          content: "修改成功！",
        });
        setInfo(res.data);
        browser.storage.local.set({
          settingInfo: res.data,
        });
      }
    });
  };

  // 获取配置
  const getSettingInfo = () => {
    settingInfo({}).then((res: any) => {
      if (res.code == 200) {
        if (res?.data) {
          setInfo(res.data);
          browser.storage.local.set({
            settingInfo: res.data,
          });
        }
      }
    });
  };
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };
  // 回车添加黑名单
  const handlePressEnter = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const value = e.currentTarget.value.trim();
    if (!value) {
      message.error("请输入 URL！");
      return;
    }
    try {
      new URL(value); // 浏览器自带 URL 类，可以校验
      setListLoading(true);
      addClient({ domain: value }).then((res) => {
        setListLoading(false);
        if (res.code == 200) {
          message.open({
            type: "success",
            content: "添加成功！",
          });
          setInputValue("");
          getWordList();
        }
      });
    } catch (err) {
      message.error("请输入合法的 URL！");
    }
  };

  useEffect(() => {
    getSettingInfo();
    getWordList();
  }, []);
  return (
    <Flex vertical gap={token.padding} className="knowledge-web">
      {/* 知识库菜单开关 */}
      <Spin spinning={listLoading}>
        <Card>
          <Flex vertical gap={token.paddingSM}>
            <Flex align="center" justify="space-between">
              <Text style={{ fontSize: token.fontSizeLG }}>开启网页数据自动采集</Text>
              <Switch value={info?.defaultStorage} onChange={switchChange} />
            </Flex>
            <Text type="secondary">
              开启后，登陆状态下，智能AI办公助手自动将用户打开浏览的网页地址及该网页页面内的信息数据等存入知识库
            </Text>
          </Flex>
          <Divider />
          {/* 网站禁用列表 */}
          {info.defaultStorage && (
            <div>
              <Flex justify="space-between">
                <Text strong>禁止采集以下网址内容到知识库</Text>
                <Input
                  value={inputValue}
                  onChange={handleChange}
                  placeholder="输入URL按回车添加"
                  style={{ width: "300px" }}
                  onPressEnter={handlePressEnter}
                />
              </Flex>
              {urlList.records && urlList.records.length > 0 ? (
                <Flex vertical gap={token.padding} style={{ marginTop: token.margin }}>
                  <List
                    dataSource={urlList.records}
                    renderItem={(item: any) => (
                      <List.Item className="list-item" key={item.id}>
                        <Text>{item.domain}</Text>
                        <Tooltip
                          placement="top"
                          title="移除黑名单"
                          getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                        >
                          <DeleteOutlined
                            className="colse"
                            style={{ color: token.colorTextDescription, cursor: "pointer" }}
                            onClick={() => {
                              delWebsite(item.id);
                            }}
                          />
                        </Tooltip>
                      </List.Item>
                    )}
                  />
                  <Pagination
                    showSizeChanger
                    onChange={(page, pageSize) => {
                      setSearchParams({ ...searchParams, pageNum: page, pageSize });
                    }}
                    style={{ justifyContent: "end" }}
                    current={urlList.current}
                    pageSize={urlList.size}
                    total={urlList.total}
                    showTotal={(total) => `共 ${total} 条`}
                  />
                </Flex>
              ) : (
                <EmptyData description={"暂无数据"} />
              )}
            </div>
          )}
        </Card>
      </Spin>
    </Flex>
  );
};

export default Knowledge;
