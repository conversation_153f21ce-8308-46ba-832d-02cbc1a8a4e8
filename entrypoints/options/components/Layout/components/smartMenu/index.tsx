import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Button, List, Typography, <PERSON>lex, Card, Divider, theme, Image, message, Spin } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { wordList, editWord, delWord } from "@/api/smartMenu";
import "./index.less";
import EmptyData from "@/components/EmptyData";
const { Text } = Typography;
const { useToken } = theme;
const SmartMenu = () => {
  const { token } = useToken();
  const [menuData, setMenuData] = useState({
    isShowSmartMenu: false,
    userSwitch: {},
    websiteList: [],
  });
  const [firstData, setFirstData] = useState<any>({});
  const [listLoading, setListLoading] = useState(false);

  useEffect(() => {
    browser.storage.local.set({
      smartMenu: {
        isShowSmartMenu: menuData.isShowSmartMenu,
        userSwitch: menuData.userSwitch,
        websiteList: menuData.websiteList.map((item) => item.domainUrl),
      },
    });
  }, [menuData]);

  // 获取列表
  const getWordList = () => {
    setListLoading(true);
    wordList({}).then((res) => {
      const [, ...list] = res.data;
      setFirstData(res.data[0]);
      setListLoading(false);
      setMenuData({
        ...menuData,
        ...{ isShowSmartMenu: res.data[0].enabled, userSwitch: res.data[0], websiteList: list },
      });
    });
  };

  // 删除隐藏的网站
  const delWebsite = (id: string) => {
    setListLoading(true);
    delWord([id]).then((res) => {
      if (res.code == 200) {
        message.open({
          type: "success",
          content: "删除成功！",
        });
        setListLoading(false);
        getWordList();
      }
    });
  };

  // 开关
  const switchChange = (checked: boolean) => {
    setListLoading(true);
    setMenuData({ ...menuData, ...{ isShowSmartMenu: checked } });
    editWord({ id: firstData.id, configType: "GLOBAL", enabled: checked }).then((res) => {
      if (res.code == 200) {
        message.open({
          type: "success",
          content: "修改成功！",
        });
        setListLoading(false);
      }
    });
  };
  useEffect(() => {
    getWordList();
  }, []);
  return (
    <Flex vertical gap={token.padding} className="smart-menu">
      {/* 文本描述区域 */}
      <Card>
        <Flex justify="center" style={{ height: "120px" }}>
          <Image
            src={browser.runtime.getURL("/images/setup/image.png")}
            alt="图片"
            style={{ width: "auto", height: "120px" }}
          />
        </Flex>
      </Card>

      {/* 智能菜单开关 */}
      <Spin spinning={listLoading}>
        <Card>
          <Flex vertical gap={token.paddingSM}>
            <Flex align="center" justify="space-between">
              <Text style={{ fontSize: token.fontSizeLG }}>显示智能菜单</Text>
              <Switch value={menuData.isShowSmartMenu} onChange={switchChange} />
            </Flex>
            <Text type="secondary">开启时，文本选择后会出现快速操作菜单</Text>
          </Flex>
          <Divider />
          {/* 网站禁用列表 */}
          {menuData.isShowSmartMenu && (
            <div>
              {menuData.websiteList.length > 0 ? (
                <Flex vertical gap={token.padding}>
                  <Text strong>在网站上禁用</Text>
                  <List
                    dataSource={menuData.websiteList}
                    renderItem={(item) => (
                      <List.Item className="list-item" key={item.id}>
                        <Text>{item.domainUrl}</Text>
                        <CloseOutlined
                          className="colse"
                          style={{ color: token.colorTextDescription, cursor: "pointer" }}
                          onClick={() => {
                            delWebsite(item.id);
                          }}
                        />
                      </List.Item>
                    )}
                  />
                </Flex>
              ) : (
                <EmptyData description={"暂无数据"} />
              )}
            </div>
          )}
        </Card>
      </Spin>
    </Flex>
  );
};

export default SmartMenu;
