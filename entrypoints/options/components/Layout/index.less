@import "@/assets/styles/variables";
body,
p,
h1 {
  margin: 0px;
  padding: 0px;
}
.setup-content {
  height: calc(100vh - 40px);
  background: #f5f5f5;
  // background: var(--ant-layout-body-bg);
  min-height: 518px;
  padding: 20px;
  .setup-con-ls {
    margin: 0px auto;
    width: 1264px;
  }
  .setup-side-left {
    min-width: 240px !important;
    max-width: 240px !important;
    // height: 165px;
    // background-color: #fff;
    padding: var(--ant-padding-md) var(--ant-padding);
    border-radius: var(--ant-border-radius);
    margin-right: var(--ant-margin);
    .setup-side-title {
      display: flex;
      margin-bottom: var(--ant-margin-sm);
      padding: var(--ant-padding-xxs) 0px;
      padding-left: var(--ant-padding-md);
      img {
        background-color: #fcf9f9;
        width: 48px;
        height: 48px;
        border-radius: 50%;
        margin-right: var(--ant-margin-sm);
      }
      .setup-side-text {
        font-weight: var(--ant-font-weight-strong);
        font-size: var(--ant-font-size-xl);
        color: var(--ant-color-text);
        padding-top: var(--ant-padding-md);
      }
    }

    .setup-side-item,
    .setup-side-item-selected {
      padding-left: 29px;
      font-size: 14px;
      height: 37px;
      line-height: 37px;
      color: #333;
      cursor: pointer;
      border-radius: 10px;
    }

    .setup-side-item-selected {
      background-color: #f0f5fd;
      color: @primary-color;
    }
    .ant-menu-item {
      margin-bottom: var(--ant-margin-sm);
      margin-left: 0px;
      margin-right: 0px;
      padding-left: var(--ant-padding) !important;
      // padding: var(--ant-padding-xxs);
      height: 48px;
      .ant-menu-title-content {
        color: var(--ant-color-text);
        font-size: var(--ant-font-size);
        font-weight: 500;
      }
    }
    .ant-menu-item-selected {
      .ant-menu-title-content {
        color: var(--ant-color-primary-text);
      }
    }
  }
  .setup-content-right {
    /* 右侧功能区域 */
    height: 100%;
    // flex: 1 1 100%;
    width: 1008px;
    padding: var(--ant-padding-xl);
    background-color: #fff;
    border-radius: var(--ant-border-radius-lg);
    .pagination {
      display: flex;
      justify-content: end;
      margin-top: var(--ant-margin-sm);
    }
    .setup-content-header {
      /* 右侧设置内容header */
      align-items: center;
      border-bottom: 1px solid var(--ant-color-split);
      padding-bottom: var(--ant-margin);

      .setup-content-title {
        /* 标题文本 */
        height: 40px;
        line-height: 40px;
        font-size: var(--ant-font-size-heading-2);
        background: linear-gradient(101deg, #1888ff 14%, #2f54eb 89%);
        -webkit-background-clip: text;
        font-weight: bold;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .setup-content-title-btn {
        /* 新增提示词按钮 */
        height: 35px;
        margin-left: auto;
      }
      .ant-input-group-wrapper {
        .ant-input-affix-wrapper {
          height: 40px;
          border-radius: 8px 0 0 8px !important;
        }
        .ant-input-group-addon {
          border-radius: 0 8px 8px 0 !important;
          .ant-input-search-button {
            border-radius: 0 8px 8px 0 !important;
          }
        }
      }
    }
    .setup-content-box-web {
      color: #333;
      font-size: 14px;

      .setup-web-box {
        margin-bottom: 21px;

        .setup-web-title {
          font-size: 16px;
          margin-bottom: 12px;
          line-height: 22px;
        }

        .setup-web-content {
          padding: 15px 22px 15px 18px;
          background-color: #ececec;
          border-radius: 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          line-height: 16px;

          .setup-web-content-left {
            .setup-web-content-text {
              margin-bottom: 13px;
            }

            .setup-web-content-left-title {
              font-family: @side-panel-font-family-bold;
              margin-bottom: 13px;
            }
          }

          .setup-web-content-right {
            display: flex;
            // width: 60vw;
            .setup-web-content-right-Shortcut {
              width: 82px;
              height: 28px;
              background-color: #fff;
              border-radius: 10px;
              text-align: center;
              line-height: 28px;
            }

            .setup-web-content-img {
              margin-right: 15px;
              width: 15vw;
              height: 70px;
            }

            .setup-web-content-switch {
              margin-top: 42px;
            }

            .side-setup-dropdown {
              .side-setup-dropdown-item {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .side-question-menu-item-text {
                  margin-right: 9px;
                }
              }
            }
          }
        }
      }
    }
  }

  .setup-content-right-web {
    min-height: 100vh;
  }
}

#setup-content ::-webkit-scrollbar {
  display: none !important;
}
