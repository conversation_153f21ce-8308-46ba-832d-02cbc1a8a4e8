import "@/assets/styles/content.less";
import "@/assets/styles/highlights.less";
import createSidePanelWrapper from "./sidepanel";
import { v4 as uuidv4 } from "uuid"; // 导入 v4 函数
import { createNote } from "@/utils/notes";

export default defineContentScript({
  matches: ["<all_urls>"],
  runAt: "document_end",
  main() {
    /*
     * 注入页面的JS逻辑
     * 开发者注意：
     * 1.该作用域没有this
     * 2.该作用域存在window和document对象，就是主页面对应的window和document对象
     */
    ////////// 以下是作用域全局参数
    const style = document.createElement("style");
    style.innerHTML = `
      .hypothesis-highlight {
        background-color: rgba(156, 230, 255, 0.5);
        &.is-transparent {
          background-color: rgb(208, 218, 121) !important;
          color: inherit !important;
        }
        &.hypothesis-highlight-focused {
          background-color: #ffec3d;
        }
      }
    `;
    document.head.appendChild(style);

    // 便签鼠标右键的位置
    let noteMouseX: number;
    let noteMouseY: number;
    /**
     * 监听鼠标右键，记录创建便签位置
     */
    document.addEventListener("contextmenu", function (event) {
      noteMouseX = event.clientX;
      // 鼠标右键点击位置相对于整个页面高度（包括滚动部分）的位置
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
      noteMouseY = scrollTop + event.clientY;
    });

    sessionStorage.setItem("sino-tap-key", uuidv4());

    /** 注册window加载完成事件 */
    window.addEventListener("load", async () => {
      // 组件直接注入网页
      createSidePanelWrapper();

      setInterval(() => {
        browser.runtime.sendMessage({ type: "wakeUp" });
      }, 6000000);
    });
    /** 注册高亮消息监听时间 */
    browser.runtime.onMessage.addListener(async (message) => {
      switch (message.type) {
        case "createNote":
          createNote(message, noteMouseX, noteMouseY);
          break;
        default:
          break;
      }
    });
  },
});
