import { ListenerCollection } from "../shared/listener-collection";
import { selectedRange } from "./range-util";

/**
 * 一个观察者，用于监视和缓冲文档当前选择的更改。
 */
export class SelectionObserver {
  /** 跟踪最后一个计划回调的超时 ID */
  private _pendingCallback: NodeJS.Timeout | null;
  private _document: Document;
  private _listeners: ListenerCollection;

  /**
   * 开始观察文档中当前选择的更改。
   *
   * @param callback - 当选定区域发生变化时调用的回调函数
   * @param document_ - 测试接缝
   */
  constructor(callback: (range: Range | null) => void, document_: Document = document) {
    let isMouseDown = false;

    this._pendingCallback = null;

    const scheduleCallback = (delay = 10) => {
      this._pendingCallback = setTimeout(() => {
        callback(selectedRange(document_.getSelection()));
      }, delay);
    };

    const eventHandler = (event: Event) => {
      isMouseDown = false
      if (event.type === "mousedown") {
        isMouseDown = true;
      }
      if (event.type === "mouseup") {
        isMouseDown = false;
      }

      // 如果用户使用鼠标进行选择，请等到他们释放鼠标后再报告选择更改。
      if (isMouseDown) {
        return;
      }

      this._cancelPendingCallback();

      // 在短暂延迟后安排通知。延迟有两个目的：
      //
      // - 如果此处理程序是由于 'mouseup' 事件而调用的，则选择不会更新，直到事件循环的下一次滴答。
      //   在这种情况下，我们只需要短暂的延迟。
      //
      // - 如果用户正在使用非鼠标输入（例如键盘或移动设备上的选择手柄）更改选择，这会缓冲更新并确保我们只在更新停止更改时报告一次。
      //   在这种情况下，我们需要更长的延迟。

      const delay = event.type === "mouseup" ? 10 : 100;
      scheduleCallback(delay);
    };

    this._document = document_;
    this._listeners = new ListenerCollection();

    this._listeners.add(document_, "selectionchange", eventHandler);

    // 鼠标事件在 body 上处理，因为在某些环境中（例如 VitalSource），传播可能会在到达文档之前停止。
    this._listeners.add(document_.body, "mousedown", eventHandler);
    this._listeners.add(document_.body, "mouseup", eventHandler);

    // 报告初始选择。
    scheduleCallback(1);
  }

  disconnect() {
    this._listeners.removeAll();
    this._cancelPendingCallback();
  }

  private _cancelPendingCallback() {
    if (this._pendingCallback) {
      clearTimeout(this._pendingCallback);
      this._pendingCallback = null;
    }
  }
}
