import { Guest } from "./guest";
import { Sidebar } from "./sidebar";
import { PortProvider } from "../shared/messaging";
import { Note } from "@/types/note";
import { initNotelist, jumpScrollToNotePos } from "@/utils/notes";

export function createStrokeService(noteList: Note[]) {
  // 添加右侧滚动便签数量
  const portProvider = new PortProvider();
  const sidePanelElement = document.getElementById("shadow-side-panel");
  const shadowRoot = sidePanelElement.shadowRoot;
  const el = shadowRoot.getElementById("annotator-stroke");
  if (el) {
    const sidebar = new Sidebar(el);
    portProvider.on("frameConnected", (source, port) => {
      return sidebar.onFrameConnected(source, port);
    });
  }
  // 划词服务
  let _guest = new Guest(document.body);
  initNotelist(_guest, noteList);
  jumpScrollToNotePos(_guest);
  return _guest;
}
