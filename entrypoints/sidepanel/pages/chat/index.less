@import "@/assets/styles/variables";

.chat-container {
  flex: 1;
  /* 聊天容器 */
  height: 100%;
  min-width: @side-panel-route-min-width;
  // overflow: hidden auto;
  position: relative;
  .ant-tooltip-content{
    white-space: normal;
  }
  .chat-list {
    /* 聊天消息区 */
    flex: 1;
    flex-basis: 0;
    gap: var(--ant-margin-sm);
    overflow: hidden auto;
    padding-top: var(--ant-padding-xs);
    scroll-behavior: smooth;

    .chat-item {
      /* 每一个聊天项的公共样式 */
      width: fit-content;
      margin-bottom: var(--ant-margin-md);
      max-width: 100%;
      min-width: 0;
      font-size: var(--ant-font-size);
      line-height: var(--ant-line-height);
      padding: var(--ant-padding-sm) var(--ant-padding);
    }

    .chat-question {
      /* 聊天提问 */
      background-color: var(--ant-color-primary-bg);
      border-radius: var(--ant-border-radius-lg) 1px var(--ant-border-radius-lg) var(--ant-border-radius-lg);
      color: #2b2b2b;
      margin-left: auto;
      .markdown-body {
        /* 特殊设置一下提问时markdown组件的字体颜色 */
        color: inherit;
      }
    }

    .answer-block {
      position: relative;
      .chat-answer {
        /* 聊天回复 */
        border-radius: 1px var(--ant-border-radius-lg) var(--ant-border-radius-lg);
        margin-right: auto;
        padding: 0px;
      }

      div.chat-toolbar {
        /* 消息工具栏 */
        display: flex !important;
        flex-direction: row !important;
        flex-wrap: nowrap !important;
        gap: var(--ant-margin-xxs) !important;
        margin-top: -14px;
        .btn-hover {
          margin-right: var(--ant-margin-xxs);
        }
        .icon-oper {
          font-size: var(--antd-font-size);
          color: var(--ant-color-text-tertiary;);
        }
      }
    }
  }

  .chat-input-panel {
    /* 聊天输入面板 */
    // background: linear-gradient(to top, lighten(@primary-color, 30%), transparent);
    border-radius: 0 0 25px 25px;
    padding-top: var(--ant-padding);
    position: relative;
    // gap: 10px;
    position: relative;
    .stop {
      /* 停止按钮 */
      all: initial;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: -38px;
      .stop-btn {
        // border: 1px solid @primary-color;
        color: var(--ant-color-text);
        background: var(--ant-color-bg-container);
        border: 1px solid var(--ant-color-border);
        box-shadow:
          0px 2px 4px 0px rgba(0, 0, 0, 0.02),
          0px 1px 6px -1px rgba(0, 0, 0, 0.02),
          0px 1px 2px 0px rgba(0, 0, 0, 0.03);
        &:hover {
          color: var(--ant-color-text);
          // background-color: #F5E8FF;
          opacity: 0.85;
        }
      }
    }

    .top-toolbar {
      /* 聊天工具条 */
      height: 28px;
      margin-bottom: var(--ant-margin-xs);

      .top-toolbar-left {
        flex: 1;
        width: 59% !important;
        all: initial;
        height: 26px;
        .ant-select {
          --ant-select-active-outline-color: none !important;
        }
        .btn-icon {
          min-width: 0px !important;
          width: 28px;
          height: 28px;
          border: none;
          .icon {
            color: var(--ant-color-text);
            font-size: var(--ant-font-size-xl);
          }
          &:hover {
            background: var(--ant-color-primary-bg);
            .icon {
              color: var(--ant-color-text);
            }
          }
        }
        .upload-file {
          margin-left: var(--ant-margin-xs);
        }
        .tips {
          color: var(--ant-color-text);
          font-size: var(--ant-font-size);
          margin-right: var(--ant-margin-xs);
        }
        .upload-desc {
          font-size: var(--ant-font-size-sm);
          color: var(--ant-color-text-quaternary);
          margin-left: var(--ant-margin-xs);
        }
      }

      .chat-container-assistant {
        /*position: absolute;
        right: 0;
        top: -60px;
        z-index: 101;*/
        position: relative;
        max-width: 60%;
        min-width: 105px;

        img {
          position: absolute;
          z-index: 99;
          top: 5px;
          left: 8px;
          width: 14px;
        }
        .ant-select {
          width: 100%;
          height: 28px;
        }
        .ant-select-selector {
          padding: 0 12px 0 28px;
          // background: lighten(@primary-color, 35%);
          border: none;
          background: #f7f7f7 !important;
          border-radius: 16px !important;
          .ant-select-selection-item,
          .ant-select-selection-placeholder {
            line-height: 24px;
            font-size: var(--ant-font-size-sm);
          }
        }
        .agent-name {
          display: inline-block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 200px;
        }
        .ant-select-dropdown {
          min-width: 200px !important;
          // width: auto !important;
        }
      }

      .top-toolbar-right {
        gap: var(--ant-margin-xs);
        .btn-icon {
          min-width: 0px !important;
          width: 28px;
          height: 28px;
          border: none;
          .icon {
            color: var(--ant-color-text);
            font-size: var(--ant-font-size-xl);
          }
          &:hover {
            background: var(--ant-color-primary-bg);
            .icon {
              color: var(--ant-color-text);
            }
          }
        }
      }
      .chat-clear,
      .chat-history {
        /* 开启新聊天按钮 */
        &.not-allowed {
          cursor: not-allowed;
        }
      }

      .chat-history {
        background-color: transparent;
      }
    }

    // .text-input-border {
    //   border-width: 1px;
    //   border-style: solid;
    //   border-color: #d9d9d9;
    // }

    .text-input {
      border: 1px solid var(--ant-color-border);
      border-radius: var(--ant-border-radius-lg);
      position: relative;
      &:hover {
        border-color: var(--ant-color-primary-hover);
      }
      &:focus {
        border-color: var(--ant-color-primary-active);
      }
      .text-input-content{
        border: 1px solid var(--ant-color-border-secondary);
        border-radius: var(--ant-border-radius-sm);
        padding: var(--ant-padding-xs);
        margin: var(--ant-margin-xs) var(--ant-margin-sm) 0px ;
        min-height: 46px;
        position: relative;
        .quote-close{
          display: none;
          color: var(--ant-color-text-description);
          opacity: 0.5;
          position:absolute;
          right:-8px;
          top: -8px;
        }
        &:hover{
          .quote-close{
            display: block;
          }
        }
        .quote-tit{
          color: var(--ant-color-text-description);
          font-size: var(--ant-font-size-sm)
        }
        .quote-text{
          color: var(--ant-color-text-placeholder);
          font-size: var(--ant-font-size-sm);
          margin-top: var(--ant-margin-xxs);
          overflow: hidden;
          text-overflow:ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
      @keyframes changeHeight {
        from {
          height: 0;
        }

        to {
          height: 66px;
          /* 或者具体的高度 */
        }
      }

      .prompt-select {
        margin: 0px var(--ant-margin-sm);
        padding: var(--ant-padding-sm) 0px 0px;
        flex: 1;
        .ant-tag-borderless {
          display: flex;
          min-width: 210px;
          max-width: 280px;
          overflow-y: auto;
          justify-content: space-between;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .knowledge-base {
        margin: 0px var(--ant-margin-sm);
        padding: var(--ant-padding-sm) 0px 0px;
        box-sizing: border-box;
        border-bottom: 1px solid var(--ant-color-split);
        flex: 1;
        position: relative;
        .knowledge-base-info {
          animation: changeHeight 0.3s ease forwards;
          gap: var(--ant-margin-xxs);
          overflow-x: auto;
          flex: 1;
        }
        .right-icon,
        .left-icon {
          position: absolute;
          top: var(--ant-padding-sm);
          width: 25px;
          height: 52px;
        }
        .right-icon {
          right: 0px;
          background: linear-gradient(270deg, #ffffff 36%, rgba(255, 255, 255, 0.18) 67%, rgba(255, 255, 255, 0) 100%);
        }
        .left-icon {
          left: 0px;
          background: linear-gradient(270deg, rgba(255, 255, 255, 0) 36%, rgba(255, 255, 255, 0.18) 67%, #ffffff 100%);
        }
        .left,
        .right {
          min-width: 0px;
          width: 20px;
          height: 20px;
          position: absolute;
          font-size: var(--ant-font-size);
          border: none;
          z-index: 10;
          background: var(--ant-color-bg-base);
          box-shadow:
            0px 2.5px 5px -1px rgba(0, 0, 0, 0.12),
            0px 5px 6.25px 0px rgba(0, 0, 0, 0.08),
            0px 1.25px 12.5px 0px rgba(0, 0, 0, 0.05);
          .anticon {
            font-size: var(--ant-font-size);
            color: var(--ant-color-text);
          }
        }
        .left {
          left: 2px;
          top: 50%;
          transform: translateY(-50%);
        }
        .right {
          right: 2px;
          top: 50%;
          transform: translateY(-50%);
        }
        .knowledge-load {
          width: 24px;
          height: 48px;
          margin-right: var(--ant-margin-xs);
          margin
          > div {
            align-items: center;
          }
        }

        .sino-relation-icon {
          color: #813ce0;
          padding-top: 1.5px;
        }

        .extend-icon {
          width: 24px;
          height: 24px;
        }
        .knowledge-content-base {
          background: var(--ant-color-fill-tertiary);
          border-radius: var(--ant-border-radius);
          padding: 0px var(--ant-padding-xs);
          box-sizing: border-box;
          height: 52px;
          flex: 1;
          max-width: 240px;
          &:hover {
            .close {
              display: block;
            }
          }
        }
        .knowledge-content-base-flex {
          // width: calc(100% - 28px);
          position: relative;
          gap: var(--ant-margin-xxs);
          .close {
            z-index: 10;
            background: #fff;
            color: var(--ant-color-text-tertiary);
            font-size: var(--ant-font-size-lg);
            position: absolute;
            right: -4px;
            top: 4px;
            border-radius: 50%;
            display: none;
          }
        }
        .knowledge-content-base-item {
          white-space: nowrap;
          // width: 94%;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .knowledge-content-base-width {
          width: 108px !important;
          max-width: 108px !important;
        }
        .knowledge-base-item {
          width: 100%;
          white-space: nowrap;
          width: 90%;
          margin-left: var(--ant-margin-xs);
          overflow: hidden;
          text-overflow: ellipsis;
          height: 52px;
        }

        .knowledge-base-title {
          font-size: var(--ant-font-size);
          color: var(--ant-color-text);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .first-title {
          padding-left: 4px;
        }

        .two-title {
          font-size: var(--ant-font-size-sm);
          color: var(--ant-color-text-tertiary);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .knowledge-keyword {
        .knowledge-keyword-ul {
          display: flex;
          flex-wrap: nowrap;
          height: 20px;
          width: 94%;
          padding-left: 10px;

          li {
            padding: 2px 6px;
            background: #f9f0ff;
            border-radius: 2px;
            color: #813ce0;
            font-size: 12px;
            margin-right: 8px;
            cursor: pointer;
          }
        }
      }

      text-align: left;

      .text-input-mentions {
        border-radius: var(--ant-border-radius-lg);
        height: 108px;
        padding: var(--ant-padding-sm);
        border: 0px;
        &:focus,
        &:focus-within {
          box-shadow: none;
        }
        > textarea {
          padding: 0px;
          height: 65px;
          &:focus {
            box-shadow: none;
            border: none;
          }
        }
      }

      .ant-mentions-dropdown {
        z-index: 9999 !important;
      }

      /* 文本输入区 */
    }
    .operate {
      width: calc(100% - 2px);
      height: 32px;
      border-radius: var(--ant-border-radius);
      position: absolute;
      left: 1px;
      bottom: 2px;
      background: #fff;
    }
    .send-btn {
      /* 发送按钮 */
      position: absolute;
      right: 12px;
      bottom: 10px;
      border: none;
      padding: 0px !important;
      height: auto;
      &.active,
      &.hover {
        opacity: 0, 85;
        border: none;
      }
      &:disabled {
        background-color: #fff;
      }
    }
    .cue-word-btn {
      width: auto;
      border: none;
      padding: 0px !important;
      height: auto;
      position: absolute;
      left: 12px;
      bottom: 12px;
      .icon {
        fill: var(--ant-color-text-tertiary);
        font-size: var(--ant-font-size-xl);
      }
      &:disabled {
        background-color: #fff;
      }
    }
    .cue-word-btn-hover {
      &:hover {
        .icon {
          fill: var(--ant-color-text);
        }
      }
    }
    .chat-textarea {
      .chat-cue {
        width: 100%;
        position: absolute;
        left: 0px;
        top: -188px;
        .ant-dropdown-menu-submenu{
          left:0px !important;
          ul{
            position: absolute;
            left: 150px;
            max-height: 180px;
            overflow-y: auto;
            width: 150px;
            top:-5px;
          }
        }
        .ant-dropdown {
          width: 100%;
          position: absolute;
          left: 0px !important;
          top: 0px !important;
          max-height: 180px;
          .ant-dropdown-menu {
            height: 100%;
          }
          .ant-dropdown-menu-item-group-title {
            padding: var(--ant-padding-xs);
          }
          .ant-dropdown-menu-item-group-list {
            margin: 0px !important;
            height: 134px !important;
            overflow-y: auto;
          }
        }
      }
      .chat-cue-nodata {
        width: 100%;
        padding: var(--ant-padding-xxs);
        border-radius: var(--ant-border-radius-lg);
        background: var(--ant-color-bg-container);
        position: absolute;
        left: 0px;
        top: -188px;
        height: 180px;
        z-index: 111;
        box-shadow:
          0px 8px 10px -5px rgba(0, 0, 0, 0.08),
          0px 16px 24px 2px rgba(0, 0, 0, 0.04),
          0px 6px 30px 5px rgba(0, 0, 0, 0.05);
        .chat-cue-tit {
          padding: var(--ant-padding-xs) var(--ant-padding-sm);
          font-size: var(--ant-font-size);
          line-height: var(--ant-line-height);
          color: var(--ant-color-text-tertiary);
        }
        .chat-cue-con {
          width: 100%;
          max-height: 120px;
          overflow-y: auto;
        }
        .chat-cue-text {
          padding: var(--ant-padding-xxs) var(--ant-padding-sm);
          font-size: var(--ant-font-size);
          color: var(--ant-color-text);
          cursor: pointer;
          border-radius: var(--ant-border-radius-sm);
          flex: 1 1 100%;
          span {
            width: 100%;
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          &:hover {
            background: var(--ant-control-item-bg-hover);
          }
        }
      }
    }
  }

  .chat-input-panel-responding {
    .send-btn {
      &:disabled {
        border-color: #f5f5f5 !important;
        background-color: #f5f5f5;
      }
    }
  }

  .ant-mentions-outlined {
    &:hover,
    &focus,
    &:focus-within {
      border-color: @primary-color !important;
    }
  }

  // .ant-mentions-outlined.ant-mentions-disabled, .ant-mentions-outlined[disabled]{
  //   background-color: #fff;
  // }
}

.prompt-item {
  display: flex;
}
.ant-spin-container {
  /* 修改主色调 */
  .ant-spin-main {
    /* 修改旋廓的颜色 */
    background-color: #f5222d;
    .ant-spin-dot-item {
      /* 修改为你想要的颜色 */
    }
  }
}
.sino-upload-file {
  display: inline-block;
  width: 100%;
  > div.ant-upload {
    width: 100%;
  }
}
