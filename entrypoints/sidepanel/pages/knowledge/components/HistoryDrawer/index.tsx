import React, { useCallback, useEffect, useState } from "react";
import { DeleteSVGIcon } from "@/config/menu/note.tsx";
// import { EditSVGIcon } from "@/config/menu/chat.tsx";
import { getDay } from "@/utils/notes.ts";
import { debounce } from "@/utils/debounce.ts";
import { UserInfo } from "@/utils/auth.ts";
import { Drawer, Input, Modal, Tooltip } from "antd";

import "./index.less";

const HistoryDrawer: React.FC<{
  currentAi: string;
  userInfo: UserInfo;
  visible: boolean;
  handleClose: () => void;
  handleBack: (info: { id: string; name: string; introduction: string }) => void;
}> = ({ currentAi, userInfo, visible, handleClose, handleBack }) => {
  const [historySearchText, setHistorySearchText] = useState<string>("");
  const [searchParams, setSearchParams] = useState({
    appKey: import.meta.env["VITE_AI_CHAT_SECRET"],
    user: "anonymous",
    query: "",
  });
  const [init, setInit] = useState(false);
  const [historyList, setHistoryList] = useState([]);

  const [openRename, setOpenRename] = useState(false);
  const [conversationName, setConversationName] = useState("");
  const [conversationId, setConversationId] = useState("");

  /** 处理普通Web请求的hook */
  const fetchRequest = useFetchRequest();

  const handleHistorySearch = (params = searchParams, first = false) => {
    fetchRequest({
      api: "getConversationHistoryList",
      params,
      callback: (res) => {
        if (first && res.data.length > 0) {
          setInit(true);
        }
        setHistoryList(res.data);
      },
    });
  };

  const debounceHistorySearch = useCallback(debounce(handleHistorySearch, 1000), []);
  // input change
  const historyInputChange = (e) => {
    const value = e.target.value.trim();
    setHistorySearchText(value);

    const updateSearchParams = {
      ...searchParams,
      query: value,
    };
    setSearchParams(updateSearchParams);
    debounceHistorySearch(updateSearchParams);
  };

  // delete 接口方法
  const deleteHistoryRequest = (id, search = false) => {
    fetchRequest({
      api: "deleteConversationById",
      params: {
        appKey: currentAi,
        user: userInfo?.id || "anonymous",
        id,
      },
      callback: () => {
        search && handleHistorySearch();
      },
    });
  };

  const deleteHistoryChat = (e, id) => {
    e.stopPropagation();
    deleteHistoryRequest(id, true);
  };

  const renameHistoryChat = (e, row) => {
    e.stopPropagation();

    setConversationId(row.id);
    setConversationName(row.name);
    setOpenRename(true);
  };

  const handleChangeConversationName = (e) => {
    setConversationName(e.target.value.trim());
  };

  const handleCancelConversation = () => {
    setOpenRename(false);
    setConversationName("");
  };

  // 重命名提交
  const handleSubmitConversation = () => {
    fetchRequest({
      api: "conversationRename",
      params: {
        appKey: currentAi,
        user: userInfo?.id || "anonymous",
        id: conversationId,
        name: conversationName,
      },
      callback: () => {
        handleCancelConversation();
        handleHistorySearch();
      },
    });
  };

  // 批量删除
  const handleBatchDelete = () => {
    if (!historyList.length) return;

    for (const history of historyList) {
      try {
        deleteHistoryRequest(history.id);
      } catch (error) {
        console.error(error.message);
      }
    }

    setTimeout(() => {
      handleHistorySearch();
    }, 100);
  };

  useEffect(() => {
    const updateSearchParams = {
      appKey: currentAi,
      user: userInfo?.id || "anonymous",
      query: "",
    };
    !searchParams.appKey && setSearchParams(updateSearchParams);
    visible && handleHistorySearch(updateSearchParams, true);

    !visible && setInit(false);
  }, [visible]);

  return (
    <>
      <Drawer
        title={
          <div className="history-drawer-title">
            问答历史<span>({historyList.length})</span>
          </div>
        }
        placement="bottom"
        className="chat-history-drawer"
        closable={true}
        maskClosable={false}
        getContainer={() => {
          if (document.getElementById("shadow-side-panel")) {
            const shadowDom = document.getElementById("shadow-side-panel").shadowRoot;
            return shadowDom.querySelector(".side-panel-content");
          }
        }}
        onClose={handleClose}
        open={visible}
      >
        <div className="history-panel">
          {init && (
            <div className="search-area">
              <div className="search-input">
                <img
                  src={browser.runtime.getURL(`/images/note/searchDefaultIcon.png`)}
                  alt=""
                  className="search-icon"
                />
                <Input
                  allowClear
                  placeholder="搜索"
                  style={{ width: "100%" }}
                  value={historySearchText}
                  onChange={historyInputChange}
                />
              </div>
              <div className="delete" onClick={handleBatchDelete}>
                {DeleteSVGIcon({ height: 20, width: 20 })}
              </div>
            </div>
          )}

          <div className="history-box">
            {historyList.length === 0 && <div className="no-data">暂无会话历史记录呢</div>}
            {historyList.map((item) => (
              <div className="history-item" key={item.id} onClick={() => handleBack(item)}>
                <div className="history-item-head">
                  <span className="title">{item.name}</span>
                  <span className="time">{getDay(item.created_at)}</span>
                </div>
                <div className="info-bottom">
                  <div className="desc">{item.introduction}</div>
                  <div className="action">
                    <span>
                      <Tooltip
                        placement="top"
                        title="编辑标题"
                        getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                      >
                        <div className="round" onClick={(e) => renameHistoryChat(e, item)}>
                          {/* {EditSVGIcon} */}
                        </div>
                      </Tooltip>
                    </span>
                    <span>
                      <Tooltip
                        placement="top"
                        title="删除"
                        getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                      >
                        <div className="round" onClick={(e) => deleteHistoryChat(e, item.id)}>
                          {DeleteSVGIcon({ height: 20, width: 20 })}
                        </div>
                      </Tooltip>
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Drawer>
      <Modal
        title="编辑标题"
        open={openRename}
        className="chat-history-modal"
        width={"90%"}
        centered={true}
        okText="确认"
        cancelText="取消"
        onOk={handleSubmitConversation}
        onCancel={handleCancelConversation}
        getContainer={() => {
          if (document.getElementById("shadow-side-panel")) {
            const shadowDom = document.getElementById("shadow-side-panel").shadowRoot;
            return shadowDom.querySelector(".side-panel-content");
          }
        }}
      >
        <div>
          <Input maxLength={200} showCount={true} value={conversationName} onChange={handleChangeConversationName} />
        </div>
      </Modal>
    </>
  );
};

export default HistoryDrawer;
