@import "@/assets/styles/variables";

.ant-card {
  /* 提示词卡片 */
  // box-shadow: rgba(0, 0, 0, 0.1) 0 0 10px 1px;
  min-width: 200px;
  width: 100%;
  background: linear-gradient(206deg, #f9f0ff 0%, #fdf9ff 100%) !important;
  border-radius: 12px !important;
  border: 1px solid #f7f7f7 !important;
  padding: 11px 16px !important;
  .ant-card-head {
    /* 提示词卡片头部 */
    // background-color: #F0F5FD;
    min-height: 40px;
    height: 40px;

    border-bottom: none;
    .ant-card-head-wrapper {
      justify-content: space-between;
      .ant-card-head-title {
        /* 提示词标题 */
        align-items: center;
        display: flex;
        flex: 1 !important;
        font-size: 14px;
        gap: 5px;
        margin-right: 5px;
        min-width: 1px;

        .title {
          display: inline-block;
          font-family: @side-panel-font-family-bold;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          min-width: 1px;
          font-size: 16px;
          color: #121212;
          vertical-align: text-bottom;
          line-height: 22px;
          padding-left: 4px;
        }
      }

      .ant-card-extra {
        /* 操作区 */
        margin: 0;
        display: flex;
        gap: 5px;
        /* 卡片头部的操作按钮 */
        align-items: center;
        justify-content: center;
        flex-wrap: nowrap;

        .collection-options {
          /* 收藏选项 */
          display: flex;
          align-items: center;
          gap: 5px;
        }
      }
    }
  }

  .ant-card-body {
    /* 提示词卡片主体 */
    padding: 0px;
    .title {
      vertical-align: text-bottom;
      padding-left: 4px;
    }
    .prompt-card-content {
      /* 提示词内容，超出省略 */
      font-size: 14px;
      color: #787878;
      line-height: 22px;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.5em;
      height: 4.5em;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      word-break: break-all;
    }

    .prompt-card-footer {
      /* 提示词卡片页脚 */
      align-items: center;
      display: flex;
      font-size: 12px;
      height: 1.5em;
      line-height: 1.5em;
      margin-top: 20px;
      // gap: 5px;

      .name-avatar,
      .name-avatar-img {
        /* 创建人姓名头像 */
        border-radius: 5px;
        color: #fff;
        font-family: @side-panel-font-family-bold;
        text-align: center;
        height: 18px;
        width: 18px;
      }
      .name-avatar {
        background-color: @primary-color;
      }

      .times {
        /* 收藏次数 */
        color: @primary-color;
        font-size: 14px;
        font-weight: bolder;
        padding: 2px;
      }

      .date {
        /* 提示词创建时间 */
        color: darken(@disabled-color, 15%);
        font-family: @side-panel-font-family-bold;
        margin-left: auto;
      }
    }
  }
  .footer-tootle-wcl {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  .footer-circle-wcl {
    background-color: #b7b7b7;
    border-radius: 4px;
    width: 18px;
    color: #fff;
    text-align: center;
    display: inline-block;
    margin-right: 8px;
  }
}
.prompt-page-container .prompt-list .ant-card .ant-card-head {
  padding: 0px !important;
}
