import React, { useState } from "react";
import { Card, message, Popconfirm, Tooltip, Dropdown, Typography, Space } from "antd";
import type { MenuProps } from "antd";
import { Prompt } from "@/types/prompt";
import { delPrompt, updatePromptCollectionStatus, updatePromptShareStatus } from "@/api/prompt.ts";
import { promptIconSvg, love, noLove } from "@/config/menu/index";
import "./index.less";
import { knowdgeSVGIcon } from "@/config/menu/knowdge";
import { useDebounceFn, useLockFn } from "ahooks";

/** 提示词卡片组件属性 */
export type PromptCardProps = {
  /**
   * 卡片类型：
   * public：公共卡片，
   * personal：我的卡片，
   * collection：收藏卡片
   */
  type?: string;
  /** 提示词数据 */
  prompt: Prompt;
  /** 是否允许编辑操作：type为personal时生效 */
  editable?: boolean;
  /** 是否允许删除操作：type为personal时生效 */
  deletable?: boolean;
  /** 是否允许共享操作：type为personal时生效 */
  releasable?: boolean;
  /** 编辑操作时的回调：type为personal时生效 */
  onEdit?: (prompt: Prompt) => void;
  /** 卡片操作成功后的回调 */
  onSubmitSuccess?: () => void;
  collHandler?: () => void;
};

/** 提示词卡片组件 */
const PromptCard: React.FC<PromptCardProps> = ({
  type,
  prompt,
  editable = false,
  deletable = false,
  releasable = false,
  onEdit,
  onSubmitSuccess,
  collHandler,
}) => {
  // 由于收藏操作完成后不能刷新页面，所以组件自己控制提示词的收藏状态
  const [collection, setCollection] = useState<boolean>(prompt.collection);
  const userInfo = JSON.parse(localStorage.getItem("user"));
  const fetchRequest = useFetchRequest();

  const CardOptions = () => {
    const getOptions = () => {
      const options = [];
      if (type === "public" || type === "collection") {
        if (prompt.createBy !== userInfo?.id) {
          options.push({
            title: collection ? "取消收藏" : "收藏",
            onClick: () => {
              // handlePromptCollect(prompt);
            },
            children: (
              <div className="collection-options">
                {prompt.collectionCount}
                <img
                  src={
                    collection
                      ? browser.runtime.getURL("/images/prompt/collected.svg")
                      : browser.runtime.getURL("/images/prompt/uncollected.svg")
                  }
                  alt=""
                  // onClick={() => handlePromptCollect(prompt)}
                />
              </div>
            ),
          });
        }
        return options;
      }
      if (editable) {
        options.push({
          title: "编辑",
          iconSelected: browser.runtime.getURL("/images/prompt/edit-selected.svg"),
          iconUnSelected: browser.runtime.getURL("/images/prompt/edit-selected.svg"),
          onClick: () => {
            handlePromptEdit(prompt);
          },
        });
      }
      if (deletable) {
        options.push({
          title: "删除",
          iconSelected: browser.runtime.getURL("/images/prompt/delete-selected.svg"),
          iconUnSelected: browser.runtime.getURL("/images/prompt/delete-unselected.svg"),
          children: (
            <Popconfirm
              title="确认删除该提示词？"
              onConfirm={() => handlePromptDelete(prompt)}
              okText="确认"
              cancelText="取消"
            >
              <img src={browser.runtime.getURL("/images/prompt/delete-selected.svg")} alt="删除" />
            </Popconfirm>
          ),
        });
      }
      if (releasable) {
        let shareOption;
        if (prompt.isRelease === 1) {
          shareOption = {
            title: "撤回",
            iconSelected: browser.runtime.getURL("/images/prompt/withdraw-selected.svg"),
            iconUnSelected: browser.runtime.getURL("/images/prompt/withdraw-unselected.svg"),
            children: (
              <Popconfirm
                title="确认从广场撤回该提示词吗？"
                onConfirm={() => handlePromptReleaseChange(0, prompt)}
                okText="确认"
                cancelText="取消"
              >
                <img src={browser.runtime.getURL("/images/prompt/withdraw-selected.svg")} alt="删除" />
              </Popconfirm>
            ),
          };
        } else {
          shareOption = {
            title: "分享",
            iconSelected: browser.runtime.getURL("/images/prompt/share-selected.svg"),
            iconUnSelected: browser.runtime.getURL("/images/prompt/share-unselected.svg"),
            children: (
              <Popconfirm
                title="确认将该提示词发布到广场吗？"
                onConfirm={() => handlePromptReleaseChange(1, prompt)}
                okText="确认"
                cancelText="取消"
              >
                <img src={browser.runtime.getURL("/images/prompt/share-selected.svg")} alt="删除" />
              </Popconfirm>
            ),
          };
        }
        options.push(shareOption);
      }
      return options;
    };

    const options = getOptions();

    return (
      <>
        {options.map((option, index) => {
          return (
            <Tooltip
              placement="top"
              title={option.title}
              key={index}
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
            >
              {option.children || <img src={option.iconSelected} alt="" onClick={option?.onClick} />}
            </Tooltip>
          );
        })}
      </>
    );
  };

  /** 处理点击收藏提示词 */
  const { run: collectHandler } = useDebounceFn(
    (prompt: Prompt) => {
      collHandler?.();
      let fetchUrl = "cancelPromptCollectionStatus";
      if (prompt.collFlag === "coll_flag_false") {
        fetchUrl = "updatePromptCollectionStatus";
      }

      fetchRequest({
        api: fetchUrl,
        params: {
          id: prompt.id,
        },
        callback: (res) => {
          if (res.code === 200) {
            onSubmitSuccess?.();
            message.open({
              type: "success",
              content: prompt.collFlag === "coll_flag_false" ? "已收藏" : "已取消收藏",
            });
            setCollection(!collection);
          }
        },
      });
    },
    { wait: 500 },
  );

  // const collectHandler = useLockFn(async (prompt: Prompt) => {
  //   // collHandler?.();
  //   let fetchUrl = "cancelPromptCollectionStatus";
  //   if (prompt.collFlag === "coll_flag_false") {
  //     fetchUrl = "updatePromptCollectionStatus";
  //   }

  //   fetchRequest({
  //     api: fetchUrl,
  //     params: {
  //       id: prompt.id,
  //     },
  //     callback: (res) => {
  //       if (res.code === 200) {
  //         onSubmitSuccess?.();
  //         message.open({
  //           type: "success",
  //           content: prompt.collFlag === "coll_flag_false" ? "已收藏" : "已取消收藏",
  //         });
  //         setCollection(!collection);
  //       }
  //     },
  //   });
  // });

  /** 处理点击收藏提示词 */
  // const handlePromptCollect = (prompt: Prompt) => {
  //   fetchRequest({
  //     api: "calcelPromptCollectionStatus",
  //     params: {
  //       id: prompt.id,
  //       collection: !collection,
  //     },
  //     callback: (res) => {
  //       if (res.code === 200) {
  //         message.open({
  //           type: "success",
  //           content:  "已收藏",
  //         });
  //         setCollection(!collection);
  //       }
  //     },
  //   });
  // };

  /** 处理点击编辑提示词 */
  const handlePromptEdit = (prompt: Prompt) => {
    onEdit?.(prompt);
  };

  /** 处理删除提示词 */
  const handlePromptDelete = (prompt: Prompt) => {
    fetchRequest({
      api: "delPrompt",
      params: [prompt.id],
      callback: (res) => {
        if (res.code == "200") {
          message.open({
            type: "success",
            content: "删除成功！",
          });
          onSubmitSuccess?.();
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  /** 处理提示词发布状态变更 */
  const handlePromptReleaseChange = (expectedStatus: number, prompt: Prompt) => {
    fetchRequest({
      api: "updatePromptShareStatus",
      params: {
        release: expectedStatus,
        ids: [prompt.id],
      },
      callback: (res) => {
        if (res.code === 200) {
          message.open({
            type: "success",
            content: "发布状态修改成功！",
          });
          onSubmitSuccess?.();
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };
  const items: MenuProps["items"] = [
    {
      key: "1",
      label: <div> 修改</div>,
    },
    {
      key: "2",
      label: <div> 删除</div>,
    },
  ];
  const editHandler: MenuProps["onClick"] = (...item) => {
    if (item[0].key === "1") {
      handlePromptEdit(item[1]);
    } else {
      handlePromptDelete(item[1]);
    }
  };
  return (
    <Card

    // extra={<CardOptions></CardOptions>}
    >
      {/* type的值 prompt_type_all-全部；prompt_type_created-创建的；prompt_type_coll-收藏的；prompt_type_square-广场；
prompt_type_user-用户的 */}
      <div className="footer-tootle-wcl">
        <div>
          {promptIconSvg}
          {/* <img src={browser.runtime.getURL("/images/prompt/prompt-card-title.svg")} alt="" /> */}
          <span className="title">{prompt.title}</span>
        </div>
        <div>
          {prompt.type === "prompt_type_public" && (
            <span className="times">{!!prompt.collectionCount && prompt.collectionCount}</span>
          )}
          {prompt.type == "prompt_type_public" && (
            <span
              style={{ verticalAlign: "text-top", paddingLeft: 4 }}
              onClick={() => {
                collectHandler(prompt);
              }}
            >
              <Tooltip placement="bottom" title={prompt.collFlag === "coll_flag_true" ? "取消收藏" : "收藏"}>
                {prompt.collFlag === "coll_flag_true" ? love : noLove}
              </Tooltip>
            </span>
          )}
        </div>
      </div>
      <p className="prompt-card-content">{prompt.content}</p>
      <div className="prompt-card-footer">
        <>
          {/* {prompt.type === 0 && (
            <img className="name-avatar-img" src={browser.runtime.getURL("/images/logo.png")} alt="" />
          )}
          {prompt.type === 1 && <div className="name-avatar">{prompt.createName ? prompt.createName[0] : ""}</div>}
          <span>{prompt.type === 0 ? "助手" : prompt.createName}</span> */}
        </>
        {/* 
        {type === "prompt_type_square" && (
          <span>
            <span>
              被收藏<span className="times">{prompt.collectionCount}</span>次
            </span>
          </span>
        )} */}
        {/* <span className="date">{prompt?.createTime || "1970-01-01 00:00:00"}</span> */}

        <div className="footer-tootle-wcl">
          <span>{prompt.agentName}</span>
          {/* 我创建的 */}
          {prompt.category === "prompt_type_created" && (
            <Dropdown
              placement="bottom"
              autoAdjustOverflow={true}
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
              menu={{
                items,
                onClick: (key) => editHandler(key, prompt),
                selectable: true,
              }}
              arrow={false}
            >
              {knowdgeSVGIcon.hengMore}
            </Dropdown>
          )}
          {/* 广场 */}
          {prompt.category != "prompt_type_created" && prompt.type == "prompt_type_public" && (
            <div>
              <span className="footer-circle-wcl">{prompt.userFlag}</span>
              <span>{prompt.userName}</span>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default React.memo(PromptCard);
