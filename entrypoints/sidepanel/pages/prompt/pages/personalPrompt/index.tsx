/** 我的提示词页面 */
import React, { useEffect, useRef, useState } from "react";
import EmptyData from "@/components/EmptyData";
// import PromptCard from "@/entrypoints/sidepanel/pages/prompt/components/PromptCard";
import { Prompt, PromptSearchParam } from "@/types/prompt";
import { useFetchRequest } from "@/hooks/useFetchRequest.ts";
import { Button, Flex, Image, Spin, Tag, Typography } from "antd";
import { formatDate } from "@/utils/dateFormat";
import { useDebounceFn } from "ahooks";
import { PlusOutlined } from "@ant-design/icons";
import "./index.less";
import { pagePrompts } from "@/api/prompt.ts";
// import InfiniteScroll from "react-infinite-scroll-component";
// 列表查询的初始化参数
const searchParamsInit: PageAPIRequest<PromptSearchParam> = {
  pageNum: 1,
  pageSize: 500,
  entity: {
    title: "",
    status: 0,
    collection: true,
    type: "prompt_type_created_coll",
    agentId: "",
    // createBy: JSON.parse(localStorage.getItem("user"))?.id,
  },
};

// 列表数据的初始化数据
const pageDataInit: PageAPIResponse<Prompt> = {
  current: 1,
  size: 10,
  total: null,
  pages: 0,
  records: [],
  count: 0,
};

const PersonalPromptPage: React.FC<{ context }> = ({ context }) => {
  const scrollableRef = useRef(null);
  // 父组件传来的搜索参数
  const outletContext: string = context;
  // 查询数据的请求参数
  const [searchParams, setSearchParams] = useState<PageAPIRequest<PromptSearchParam>>(searchParamsInit);
  // 当前显示的提示词分页数据
  const [promptPageData, setPromptPageData] = useState<PageAPIResponse<Prompt>>(pageDataInit);
  const [loading, setLoading] = useState(false);
  const fetchRequest = useFetchRequest();
  // 监听搜索参数变化
  // useEffect(() => {
  //   setSearchParams({
  //     ...searchParams,
  //     entity: {
  //       ...searchParams.entity,
  //       title: outletContext,
  //     },
  //   });
  // }, [outletContext]);

  // 组件初次渲染或者请求参数变更时，调用接口更新数据
  useEffect(() => {
    setSearchParams({
      pageNum: 1,
      pageSize: 500,
      entity: {
        ...searchParams.entity,
        title: outletContext,
      },
    });
    handlePromptList();
  }, [outletContext]);

  // useEffect(() => {
  //   handlePromptList();
  // }, []);

  const { run: handlePromptList } = useDebounceFn(
    () => {
      let params = {
        ...searchParams,
        entity: {
          ...searchParams.entity,
          query: outletContext,
        },
      };

      setLoading(true);
      fetchRequest({
        api: "pagePrompts",
        params,
        callback: (res) => {
          setLoading(false);
          if (res.code === 200) {
            setPromptPageData(res.data);
          }
        },
      });
    },
    { wait: 500 },
  );
  return (
    <div className="personal-prompt-list">
      <Spin spinning={loading} size="default">
        {promptPageData.total === 0 ? (
          <EmptyData description={"没有找到提示词"} />
        ) : (
          // <div
          //   ref={scrollableRef}
          //   id="scrollableDiv"
          //   style={{
          //     height: 600,
          //     overflow: "auto",
          //   }}
          // >
          //   <InfiniteScroll
          //     scrollThreshold={80}
          //     dataLength={promptPageData.records.length}
          //     next={handlePromptList}
          //     hasMore={promptPageData.records.length < promptPageData.total}
          //     loader={<Divider plain>加载中...</Divider>}
          //     endMessage={promptPageData.total > 0 && <Divider plain> 已全部加载</Divider>}
          //     scrollableTarget={scrollableRef.current ? scrollableRef.current : ""}
          //   >
          <>
            <Flex className="card-content" vertical>
              {promptPageData.records.map((prompt: Prompt, index: number) => {
                return (
                  // <PromptCard
                  //   key={index}
                  //   prompt={item}
                  //   editable={false}
                  //   releasable={true}
                  //   deletable={true}
                  //   onSubmitSuccess={handlePromptList}
                  // />
                  <Flex key={index} vertical className="card-content-box">
                    <Flex justify="space-between" align="center">
                      <Flex align="center" className="prompt-card-title">
                        {prompt.levelFlag === 1 && (
                          <img className="medal-img" src={browser.runtime.getURL("/images/prompt/gold.png")} alt="" />
                        )}
                        {prompt.levelFlag === 2 && (
                          <img className="medal-img" src={browser.runtime.getURL("/images/prompt/silver.png")} alt="" />
                        )}
                        {prompt.levelFlag === 3 && (
                          <img className="medal-img" src={browser.runtime.getURL("/images/prompt/bronze.png")} alt="" />
                        )}
                        {![1, 2, 3].includes(prompt.levelFlag) && (
                          <img src={browser.runtime.getURL("/images/prompt/Word.svg")} alt="" />
                        )}

                        <Flex className="title">{prompt.title}</Flex>
                      </Flex>

                      <Tag bordered={false} color="blue" className="prompt-card-tag">
                        {prompt.agentName}
                      </Tag>
                    </Flex>
                    <Flex className="prompt-card-content">
                      <Typography.Paragraph ellipsis={{ rows: 3, expandable: false }}>
                        {prompt.content}
                      </Typography.Paragraph>
                    </Flex>
                    <Flex className="prompt-card-text" justify="space-between" align="center">
                      <Flex align="center">
                        <Image
                          preview={false}
                          src={prompt.avatar}
                          width={24}
                          height={24}
                          className="prompt-img"
                        ></Image>
                        <Typography.Text className="prompt-card-text-left">{prompt.userName}</Typography.Text>
                      </Flex>
                      <Flex className="prompt-card-text-right">{formatDate(prompt.createTime)}</Flex>
                    </Flex>
                  </Flex>
                );
              })}
            </Flex>
            {/*  </InfiniteScroll> */}

            <Flex className="btn">
              <Button
                block
                size="large"
                icon={<PlusOutlined />}
                className="submit-btn"
                onClick={() => {
                  window.open(browser.runtime.getURL("/options.html"), "_blank");
                }}
              >
                创建提示词
              </Button>
            </Flex>
          </>
          // </div>
        )}
      </Spin>
    </div>
  );
};

export default React.memo(PersonalPromptPage);
