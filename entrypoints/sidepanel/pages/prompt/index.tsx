/** 提示词页面 */
import React, { useEffect, useState, useRef, useCallback } from "react";
import { TabsProps, Input, Flex } from "antd";
// import Search from "antd/es/input/Search";
import PersonalPromptPage from "@/entrypoints/sidepanel/pages/prompt/pages/personalPrompt";
import { SearchOutlined } from "@ant-design/icons";
import TopTitle from "../../components/TopTitle";
import "./index.less";

// Tab页签数据配置
const tabItems: TabsProps["items"] = [
  {
    key: "0",
    label: "广场",
  },
  {
    key: "1",
    label: "收藏",
  },
  {
    key: "2",
    label: "我的",
  },
];

const PromptPage: React.FC = () => {
  // 路由hook
  const [currentIndex, setCurrentIndex] = useState("0");
  let userId = JSON.parse(localStorage.getItem("user"))?.id || "";
  // 组件加载默认去第一个页签
  useEffect(() => {
    userId = JSON.parse(localStorage.getItem("user"))?.id;
  }, []);
  const [searchIcon, setSearchIcon] = useState(true);
  const inputRef = useRef(null);
  const [loading, setLoading] = useState(false);
  // 搜索参数
  const [searchText, setSearchText] = useState<string>("");
  /** 处理页签变更 */
  const handleTabChange = (targetKey: string) => {
    setCurrentIndex(targetKey);
  };
  const handleNavigate = () => {
    if (chrome.tabs) {
      browser.runtime.openOptionsPage();
    } else {
      // web
      window.location.href = browser.runtime.getURL("/options.html");
    }
  };
  const inputFocus = () => {
    if (!searchIcon) return;
    setSearchIcon(false);
  };

  const inputBlur = () => {
    setSearchIcon(true);
  };
  const inputChange = (e) => {
    const value = e.target.value.trim();
    if (!value) {
      setSearchIcon(true);
    }
    setSearchText(value);
  };
  return (
    <Flex className="prompt-page-container" vertical>
      <TopTitle title="提示词"></TopTitle>
      <Flex className={`prompt-top-box ${searchText && "search"}`} vertical>
        <Input
          ref={inputRef}
          allowClear
          size="large"
          prefix={<SearchOutlined />}
          placeholder="搜索提示词"
          style={{ width: "100%" }}
          value={searchText}
          onClick={inputFocus}
          onBlur={inputBlur}
          onChange={inputChange}
        />
      </Flex>

      <PersonalPromptPage context={searchText} />
    </Flex>
  );
};

export default PromptPage;
