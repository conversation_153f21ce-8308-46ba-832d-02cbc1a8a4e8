/** OCR页面 */
import React, { useEffect, useState, useRef } from "react";
import {
  Button,
  Divider,
  Flex,
  Image,
  message,
  Typography,
  Upload,
  UploadProps,
  theme,
  Dropdown,
  Mentions,
  Tooltip,
  Spin,
  Empty,
} from "antd";
import {
  CloseOutlined,
  DeleteOutlined,
  UploadOutlined,
  FileOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  FilePptOutlined,
  CopyOutlined,
  PauseOutlined,
  RedoOutlined,
} from "@ant-design/icons";
import { RcFile } from "antd/es/upload";
import { createRoot, Root } from "react-dom/client";
import { useFetchRequest } from "@/hooks/useFetchRequest";
import useSSEChat, { GenerationProgress } from "@/hooks/useSSEChat.ts";
import AgentOutput from "@/components/AgentOutput";
import { getToken, getUserInfo, getTenantId } from "@/utils/auth.ts";
import useClipboard from "@/hooks/useClipboard";
import { imageToBase64, dataURLtoFile } from "@/utils/image";
import { crop } from "@/utils/crop";
import { OCRData, OCRProgress } from "@/types/ocr";
import TopTitle from "../../components/TopTitle";
import IconFont from "@/components/IconFont";
import NoteKnowledgeModal from "@/components/NoteGroupModal/noteKnowledge";
import MASK from "./mask";
import "@/assets/styles/variables.less";
import "./index.less";
const useOCRUpload = (setQueryCallback?: (text: string) => void) => {
  const initialData: OCRData = {
    image: "",
    texts: [],
  };
  const [progress, setProgress] = useState<OCRProgress>(1);
  const [currentData, setCurrentData] = useState<OCRData>(initialData);
  const clipboard = useClipboard();
  const fetchRequest = useFetchRequest();
  const removeBase64ImageHeader = (base64String: string) => {
    // 分割字符串，并去掉前两个分割后的部分
    const parts = base64String.split(",");
    const cleanBase64 = parts.length > 1 ? parts.slice(1).join(",") : base64String;
    // URL编码
    const urlEncoded = encodeURIComponent(cleanBase64);
    return urlEncoded;
  };
  const uploadProps: UploadProps = {
    accept: "image/png, image/jpeg, image/webp, image/gif",
    action: "",
    multiple: false,
    style: {},
    showUploadList: false,
    beforeUpload(fileObj) {
      clipboard.setCopied(false);
      const isLt2M = fileObj.size / 1024 / 1024 < 5; // 限制文件大小为5MB
      if (!isLt2M) {
        message.error("不允许超过5MB!");
        return isLt2M; // 返回 false 则会阻止文件上传
      }

      const isValidType = /\.(png|jpg|jpeg|webp|gif)$/.test(fileObj.name);
      if (!isValidType && fileObj.name != "截图文件") {
        message.error("仅支持上传 PNG、JPEG、WEBP 或 GIF 格式的文件！");
        return;
      }
      try {
        const file = new File([fileObj as Blob], fileObj.name, { type: fileObj.type });
        imageToBase64(file).then(async (imageBase64: string) => {
          setCurrentData({ image: imageBase64, texts: [] });
          let image = removeBase64ImageHeader(imageBase64);
          const ocrConfig = {
            image: image,
            language_type: "CHN_ENG",
            detect_direction: true,
            detect_language: false,
            paragraph: false,
            probability: false,
          };

          fetchRequest({
            api: "baiduocr",
            params: ocrConfig,
            callback: (ocrResult) => {
              if (ocrResult.code == "200") {
                setProgress(4);
                const texts = ocrResult?.data?.result?.texts as Array<{ text: string }>;
                setCurrentData({
                  image: imageBase64,
                  texts: texts,
                });

                // 将OCR识别的文本内容赋值给input框
                if (texts && texts.length > 0 && setQueryCallback) {
                  const recognizedText = texts.map((item) => item.text).join("\n");
                  setQueryCallback(recognizedText);
                }
              } else {
                message.open({
                  type: "error",
                  content: ocrResult.msg,
                });
              }
            },
          });
        });
      } catch (e) {
        console.debug("上传失败");
        setCurrentData({ image: "", texts: [] });

        setProgress(5);
      }
    },
  };

  return {
    progress,
    setProgress,
    currentData,
    setCurrentData,
    uploadProps,
    clipboard,
  };
};

const OCR: React.FC = () => {
  const [query, setQuery] = useState<string>(""); // 指令输入框的值
  const { currentData, uploadProps, setProgress, setCurrentData } = useOCRUpload(setQuery);
  const { token: csstoken } = theme.useToken();
  const [ocrPointsCost, setOcrPointsCost] = useState<number>(0); // 设置OCR积分消耗
  const [tags, setTags] = useState<any[]>([]); // 选中的指令
  const [promptList, setPromptList] = useState<any[]>([]); // 指令列表
  const [promptData, setPromptData] = useState<any[]>([]); // 指令原始数据
  const [dropdownVisible, setDropdownVisible] = useState<boolean>(false); // 指令下拉框是否可见
  const [userInfo, setUserInfo] = useState<any>(null); // 用户信息
  const [agentId, setAgentId] = useState<string>(import.meta.env["VITE_AI_CHAT_AGENT_ID"]); // 代理ID
  const [localFile, setLocalFile] = useState<
    Array<{
      id?: string;
      name: string;
      image?: string;
      url?: string;
      extension?: string;
      libName?: string;
      libDesc?: string;
      flag?: string;
      loading?: boolean;
      uid?: string;
    }>
  >([]); // 上传的文件列表
  const popupContainerRef = useRef<HTMLDivElement>(null); // 指令下拉框容器
  const [modalVisible, setModalVisible] = useState(false); // 知识库
  const [noteInfoData, setNoteInfoData] = useState<any>(); // 知识库弹框数据
  const [hasCopy, setHasCopy] = useState<boolean>(false);

  // SSE聊天钩子
  const sseChat = useSSEChat();

  // 结果区域相关状态
  const [contentHeight, setContentHeight] = useState<string>("130px)"); // 结果区域高度
  const resultDomRef = useRef<HTMLDivElement>(null); // 结果区域引用
  const inputDomRef = useRef<HTMLDivElement>(null); // 输入区域引用

  // 处理普通Web请求的hook
  const fetchRequest = useFetchRequest();

  async function urlToBlob(url: string) {
    return fetch(url)
      .then((response) => response.blob())
      .then((blob) => blob);
  }

  const handleImg = async function (message: any) {
    if (message.extractImg64) {
      urlToBlob(message.extractImg64).then((blob) => {
        uploadProps.beforeUpload(blob as RcFile, []);
      });
    }

    switch (message.type) {
      case "captured": {
        let { left, top, width, height, href } = message;
        const base64 = await crop(href, { left, top, width, height });
        var file = dataURLtoFile(base64, "截图文件");
        uploadProps.beforeUpload(file as RcFile, []);
        break;
      }
      default:
        break;
    }
  };

  // 初始化数据
  useEffect(() => {
    handleOrc();
    chrome.storage.local.get(["extractImg64"], function (result) {
      if (result.extractImg64) {
        urlToBlob(result.extractImg64).then((blob) => {
          uploadProps.beforeUpload(blob as RcFile, []);
        });
      }
    });
    browser.runtime.onMessage.addListener(handleImg);

    // 获取用户信息
    // 不需要存储token和tenantId
    getToken();
    getTenantId();
    getUserInfo().then((res) => {
      setUserInfo(res);
    });

    // 获取代理ID
    fetchRequest({
      api: "listAgents",
      params: {},
      callback: async (res) => {
        if (res.data && res.data.length > 0) {
          setAgentId(res.data[0].id);
          setOcrPointsCost(Number(res.data[0]?.agentScore));
        }
      },
    });

    return () => {
      browser.runtime.onMessage.removeListener(handleImg);
    };
  }, []);

  var parent = document.getElementById("orc-box");
  var upload = document.getElementById("ocr-upload");
  var newDiv = document.createElement("div");

  const handleOrc = () => {
    parent = document.getElementById("orc-box");
    upload = document.getElementById("ocr-upload");
    if (parent) {
      parent.addEventListener("dragenter", listenMouseDragenter);
      parent.addEventListener("dragover", function (event) {
        event.preventDefault(); // 阻止默认行为
      });
      parent.addEventListener("drop", listenMouseDrop);
      renderMask();
    }
  };
  let root: Root = null;
  // 渲染蒙版
  const renderMask = () => {
    if (!root) {
      root = createRoot(newDiv);
    }
    root.render(
      <React.StrictMode>
        <MASK></MASK>
      </React.StrictMode>,
    );
    parent.appendChild(newDiv);
    newDiv.style.display = "none";
  };

  // 开启截图
  const screenshotScreen = async () => {
    browser.runtime.sendMessage({ type: "screenshot" });
  };

  // 获取指令列表 - 参考chat页面实现
  const fetchInstructionList = () => {
    // 先设置下拉列表为可见，确保用户看到加载状态
    // setDropdownVisible(true);

    // 使用真实API调用获取指令列表
    fetchRequest({
      api: "getAcctIns",
      params: {
        queryContent: "",
        insShowType: "2",
      },
      callback: (res) => {
        if (res.code === 200) {
          let arrShow: any[] = [];
          res.data.forEach((item: any) => {
            if (item.accountShowFlag !== 0) {
              arrShow.push(item);
            }
          });

          const promptListData = arrShow.map((item) => ({
            key: item.id,
            label: (
              <Flex align="center" justify="space-between">
                <span>{item.name}</span>
                {!!item.pointsCost && item.pointsCost > 0 && (
                  <Flex align="center">
                    <span style={{ color: csstoken.blue }}>{item.pointsCost}&nbsp;</span>
                    <IconFont type="PointsCost" className="icon" />
                  </Flex>
                )}
              </Flex>
            ),
          }));

          setPromptList(promptListData);
          setPromptData(arrShow);

          // 确保下拉列表可见
          setDropdownVisible(true);
        } else {
          setPromptList([]);
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  // 处理指令点击
  const handlePromptClick = (e: any) => {
    const selectedItem = promptData.find((item) => item.id === e.key);
    if (!selectedItem) return;
    setDropdownVisible(false);
    const obj = {
      id: selectedItem.id,
      icon: selectedItem.iconRelaSourceId,
      title: selectedItem.name,
      content: selectedItem.tmplContent,
    };

    // 不需要设置当前AI
    setTags([obj]);
    setAgentId(selectedItem.agentId);
    // 将指令内容添加到input框的开头，并在后面添加一个换行符
    // 同时保留用户已经输入的内容
    setQuery(`${query}`);

    // 更新积分消耗
    setOcrPointsCost(selectedItem.pointsCost);
  };

  // 处理指令关闭
  const tagClose = () => {
    // 如果有标签且有指令内容
    if (tags.length > 0) {
      // 从输入框中移除指令内容（第一行及其后的换行符）
      const lines = query.split("\n");
      if (lines.length > 1) {
        // 移除第一行（指令内容）和第一个换行符
        setQuery(lines.slice(1).join("\n"));
      } else {
        setQuery("");
      }
    }
    setTags([]);
  };

  // 文件转base64
  function fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      // 成功读取文件时的回调
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result); // Base64 编码的字符串
      };

      // 读取文件失败时的回调
      reader.onerror = (error) => {
        reject(error);
      };

      // 读取文件并转为 Base64
      reader.readAsDataURL(file);
    });
  }

  // 使用现有的fileToBase64函数

  // 创建上传队列
  const uploadQueueRef = useRef<Array<{ file: RcFile; uid: string }>>([]);
  const isUploadingRef = useRef<boolean>(false);

  // 处理队列中的文件上传
  const processQueue = () => {
    if (uploadQueueRef.current.length === 0) {
      isUploadingRef.current = false;
      return;
    }

    isUploadingRef.current = true;
    const { file, uid } = uploadQueueRef.current.shift();

    // 使用Promise链式调用
    uploadSingleFile(file, uid)
      .then(() => {
        // 处理下一个文件
        setTimeout(processQueue, 500);
      })
      .catch((error) => {
        console.error("文件上传队列错误:", error);
        // 即使出错也继续处理下一个文件
        setTimeout(processQueue, 500);
      });
  };

  // 上传单个文件
  const uploadSingleFile = (file: RcFile, uid: string) => {
    return new Promise<void>((resolve, reject) => {
      let fileData: any = {
        fileName: "",
        fileStr: "",
      };

      if (!file) {
        resolve();
        return;
      }

      // 使用 async/await 来处理异步逻辑
      (async () => {
        try {
          // 文件类型检查
          const arr = file.name.split(".");
          const fileExt = arr[arr.length - 1] || "";

          // 将文件转换为Base64
          const imageBase64 = await fileToBase64(file);

          // 准备上传数据
          fileData = {
            fileName: file.name,
            fileStr: imageBase64,
            path: "/files/upload",
            agentId: agentId,
            user: userInfo?.id,
            libName: file.name, // 使用文件名作为libName，与服务器返回的字段保持一致
            libDesc: fileExt,
            flag: "file",
            fileType: "document", // 添加fileType字段，与服务器返回的字段保持一致
            loading: true,
            uid: uid, // 添加uid字段以确保唯一性
          };

          // 调用上传API
          fetchRequest({
            api: "uploadChatFile",
            params: fileData,
            file: true,
            callback: (response) => {
              if (response && response.data) {
                const res = response.data;
                if (res?.name) {
                  // 上传成功，更新文件信息
                  // 参考chat页面的实现，使用libName匹配文件
                  // 先将服务器返回的文件信息补充完整
                  res.libName = res.name;
                  res.libDesc = res.extension;
                  res.flag = "file";
                  res.fileType = "document";
                  res.loading = false;
                  res.uid = uid; // 保留uid以便于删除

                  // 使用函数式更新确保状态一致性
                  setLocalFile((prevFiles) => {
                    // 深拷贝当前文件列表
                    const updatedFiles = JSON.parse(JSON.stringify(prevFiles));
                    // 查找当前上传的文件索引，使用uid匹配
                    const fileIndex = updatedFiles.findIndex((item: { uid?: string }) => item.uid === uid);

                    if (fileIndex !== -1) {
                      // 更新文件信息，将服务器返回的所有字段都合并到原文件对象中
                      updatedFiles[fileIndex] = {
                        ...updatedFiles[fileIndex],
                        ...res,
                      };
                    }

                    return updatedFiles;
                  });
                  message.success("文件上传成功");
                } else {
                  // 上传失败，移除文件
                  setLocalFile((prevFiles) => prevFiles.filter((item) => item.uid !== uid));
                  message.error("文件上传失败");
                }
              }
              resolve(); // 上传完成后调用 resolve
            },
          });
        } catch (error) {
          console.error("文件上传错误:", error);
          setLocalFile((prevFiles) => prevFiles.filter((item) => item.uid !== uid));
          message.error("文件上传失败");
          reject(error); // 发生错误时调用 reject
        }
      })();
    });
  };

  // 处理文件上传
  const handleFileUpload = async (file: RcFile) => {
    try {
      // 检查文件数量限制
      if (localFile.length >= 5) {
        message.error("最多上传5个附件");
        return false;
      }

      // 文件大小检查
      const isLt15M = file.size / 1024 / 1024 < 15; // 限制文件大小为15MB
      if (!isLt15M) {
        message.error("不允许超过15MB!");
        return false;
      }

      // 文件类型检查
      const arr = file.name.split(".");
      const fileExt = arr[arr.length - 1] || "";
      const fileFormat = ["docx", "pptx", "xls", "xlsx", "csv", "txt", "pdf"];
      if (!fileFormat.includes(fileExt.toLowerCase())) {
        message.error("文件格式不正确!");
        return false;
      }

      // 检查是否已经添加了相同的文件
      if (localFile.some((item) => item.name === file.name)) {
        message.warning("已经添加了，不可重复添加");
        return false;
      }

      // 生成唯一ID - 使用更可靠的方式生成唯一ID
      const uid = `file-${Date.now()}-${Math.random().toString(36).substring(2, 11)}-${file.name.replace(/[^a-zA-Z0-9]/g, "")}`;

      // 先添加到文件列表中，显示加载状态
      const newFile = {
        uid,
        name: file.name,
        libName: file.name, // 使用文件名作为libName，与服务器返回的字段保持一致
        libDesc: fileExt,
        extension: fileExt,
        flag: "file",
        fileType: "document", // 添加fileType字段，与服务器返回的字段保持一致
        loading: true,
      };

      // 使用函数式更新确保状态一致性
      setLocalFile((prevFiles) => {
        // 检查是否已经有相同的文件（再次检查，以防状态更新延迟）
        if (prevFiles.some((item) => item.libName === file.name)) {
          return prevFiles;
        }
        return [...prevFiles, newFile];
      });

      // 将文件添加到上传队列
      uploadQueueRef.current.push({ file, uid });

      // 如果没有正在上传的文件，开始处理队列
      if (!isUploadingRef.current) {
        processQueue();
      }
    } catch (error) {
      console.error("文件上传错误:", error);
      message.error("文件上传失败");
    }

    return false; // 阻止默认上传行为
  };

  // 删除上传的文件
  const handleRemoveFile = (fileUid: string) => {
    setLocalFile((prevFiles) => prevFiles.filter((item) => item.uid !== fileUid));
  };

  // 计算还可以上传的文件数量
  const uploadFileMaxCount = () => {
    return 5 - localFile.length;
  };

  // 根据文件类型显示不同的图标
  const getFileIcon = (extension?: string) => {
    if (!extension) return <FileOutlined />;

    switch (extension.toLowerCase()) {
      case "pdf":
        return <FilePdfOutlined style={{ color: "#FA541C" }} />;
      case "docx":
        return <FileWordOutlined style={{ color: "#1677FF" }} />;
      case "xls":
      case "xlsx":
      case "csv":
        return <FileExcelOutlined style={{ color: "#52C41A" }} />;
      case "txt":
        return <FileTextOutlined style={{ color: "#722ED1" }} />;
      case "pptx":
        return <FilePptOutlined style={{ color: "#FA8C16" }} />;
      default:
        return <FileOutlined />;
    }
  };

  // 滚动到底部
  const scrollToBottom = () => {
    if (resultDomRef.current) {
      try {
        // 获取容器的滚动高度和可见区域高度
        const scrollHeight = resultDomRef.current.scrollHeight;
        const clientHeight = resultDomRef.current.clientHeight;
        const maxScrollTop = scrollHeight - clientHeight;

        // 如果内容高度大于可视区域，则滚动到底部
        if (maxScrollTop > 0) {
          // 使用平滑滚动
          resultDomRef.current.scrollTo({
            top: maxScrollTop,
            behavior: "smooth",
          });
        }
      } catch (error) {
        console.error("Error scrolling to bottom:", error);
      }
    }
  };

  /** 处理停止生成 */
  const handleStop = () => {
    sseChat.stop(agentId, import.meta.env["VITE_AI_CHAT_SECRET"], userInfo?.id || "anonymous");
  };

  // 监听消息更新，自动滚动
  useEffect(() => {
    // 当消息正在返回或已经完成时，自动滚动到底部
    if (sseChat.progress === GenerationProgress.RESPONDING || sseChat.progress === GenerationProgress.RENDER_FINISHED) {
      // 使用 requestAnimationFrame 确保在浏览器渲染周期中滚动
      requestAnimationFrame(() => {
        scrollToBottom();
      });
    }
  }, [sseChat.displayedText, sseChat.progress]);

  // 生成内容
  const generateContent = async () => {
    if (!query) {
      message.error("请输入文本");
      return;
    }

    // 立即设置结果区域高度
    setContentHeight("calc(100vh - 130px)");

    // 立即滚动到结果区域，参考question页面的实现
    setTimeout(() => {
      scrollToBottom();
    }, 100);
    setHasCopy(false);

    // 准备请求数据
    let question = query;
    let questionCon = query;

    // 如果有指令，处理指令内容
    if (tags && tags.length > 0) {
      if (tags[0].content && tags[0].content.includes("${content}")) {
        question = tags[0].content.replaceAll("${content}", question);
      } else if (tags[0].tmplContent && tags[0].tmplContent.includes("${content}")) {
        question = tags[0].tmplContent.replaceAll("${content}", question);
      } else if (tags[0].content) {
        question = `${tags[0].content}\n${question}`;
      } else if (tags[0].tmplContent) {
        question = `${tags[0].tmplContent}\n${question}`;
      }
    }

    // 准备文件数据
    const fileData = [];

    // 如果有上传的文件，使用上传的文件
    if (localFile.length > 0) {
      // 遍历所有上传的文件
      localFile.forEach((file) => {
        // 只处理上传成功的文件（非加载中状态）
        if (file.id) {
          fileData.push({
            type: "document",
            transfer_method: "local_file",
            upload_file_id: file.id,
          });
        }
      });
    }

    // 准备发送文件数据

    // 获取token和用户信息
    const currentToken = await getToken();
    const currentTenantId = await getTenantId();
    const currentUserInfo = await getUserInfo();

    // 准备公共参数
    const commonParams = {
      inputs: {
        query: question,
        Token: currentToken || "",
        tenantid: currentTenantId || "",
      },
      files: fileData,
      response_mode: "streaming",
      user: currentUserInfo?.id || "",
      auto_generate_name: "true",
      query: question,
    };

    // 根据是否选择指令调用不同的接口
    if (tags && tags.length > 0) {
      // 在调用前重置状态
      console.log(tags, "000");
      sseChat.reset().then(() => {
        // 指令调用 - /dify/broker/ins/stream
        sseChat.start({
          message: questionCon,
          url: "/dify/broker/ins/stream",
          headers: {
            "Content-Type": "application/json",
            Token: currentToken || "",
          },
          body: {
            insId: "1",
            bizId: tags[0].id, // 使用指令ID
            path: "/chat-messages",
            bizType: "app:ins",
            agentId: agentId,
            difyJson: commonParams,
          },
          query: {
            query: question,
            noConversationId: "1",
          },
          onFinished: () => {
            // 消息已经完成，滚动到底部
            requestAnimationFrame(() => {
              scrollToBottom();
            });
          },
          instruct: "start",
        });
      });
    } else {
      // 在调用前重置状态
      sseChat.reset().then(() => {
        // agent调用 - /dify/broker/agent/stream
        sseChat.start({
          message: questionCon,
          url: "/dify/broker/agent/stream",
          headers: {
            "Content-Type": "application/json",
            Token: currentToken || "",
          },
          body: {
            insId: "1",
            bizType: "app:agent",
            bizId: agentId, // 使用agentId
            agentId: agentId,
            path: "/chat-messages",
            difyJson: commonParams,
          },
          query: {
            query: question,
            noConversationId: "1",
          },
          onFinished: () => {
            // 消息已经完成，滚动到底部
            requestAnimationFrame(() => {
              scrollToBottom();
            });
          },
          instruct: "start",
        });
      });
    }
  };

  /** 处理点击复制按钮事件 */
  const handleCopyBtnClick = () => {
    copyText(sseChat.displayedText).then((res) => {
      res && setHasCopy(true);
    });
  };

  // 重新生成
  const handlerRefresh = () => {
    generateContent();
  };

  // 拖拽进入目标区域
  const listenMouseDragenter = (event: DragEvent) => {
    event.preventDefault();
    upload.style.filter = "blur(3px)";
    newDiv.style.display = "block";
  };

  // 拖拽元素放置在目标元素
  const listenMouseDrop = (event: DragEvent) => {
    event.preventDefault();
    upload.style.filter = "none";
    newDiv.style.display = "none";

    let dt = event.dataTransfer;
    let files = dt.files;
    if (files[0]) {
      const file = files[0] as unknown as RcFile;
      uploadProps.beforeUpload(file, []);
    }
  };

  return (
    <>
      <TopTitle title="OCR"></TopTitle>
      <Flex className="ocr-container" id="orc-box" vertical ref={resultDomRef}>
        <Flex className="ocr-uploader" id="ocr-upload" vertical ref={inputDomRef}>
          <Flex className="ocr-uploader-tip" vertical>
            从图像中提取文本、数字和公式
          </Flex>

          <Flex className="ocr-result" vertical>
            {/* 截屏按钮 */}
            <Flex className="screenshot-button">
              <Button
                icon={<IconFont type="OcrCut" className="icon" />}
                onClick={screenshotScreen}
                className="screenshot-btn"
              >
                截屏
              </Button>
            </Flex>

            {/* 上传区域 */}
            {currentData.image ? (
              <Flex className="ocr-upload-box" vertical>
                <Upload.Dragger {...uploadProps} className="upload-area">
                  <Flex className="image-preview">
                    <Image src={currentData.image} preview={false} />
                    <div className="upload-title">
                      <div className="upload-actions">
                        <Flex className="action-icons">
                          <Button
                            shape="circle"
                            icon={<UploadOutlined className="icon" />}
                            className="btn-icon"
                            title="点击上传"
                          />
                          <Button
                            shape="circle"
                            icon={<DeleteOutlined className="icon" />}
                            className="btn-icon delete-icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              setCurrentData({ image: "", texts: [] });
                              setProgress(1);
                            }}
                            title="删除"
                          />
                        </Flex>
                      </div>
                    </div>
                  </Flex>
                </Upload.Dragger>
              </Flex>
            ) : (
              <Flex className="ocr-upload-box" vertical>
                <Upload.Dragger {...uploadProps} className="upload-area">
                  <div className="upload-icon">
                    <UploadOutlined />
                  </div>
                  <p className="ocr-upload-text">点击上传或将图片拖拽到此处</p>
                </Upload.Dragger>
              </Flex>
            )}

            {/* 提示文字 */}
            <div className="ocr-hint-text">直接识别或输入指令对图片提问</div>

            {/* 指令输入框 */}

            <Flex className="ocr-input-container">
              <Flex style={{ position: "relative", width: "100%" }}>
                <Flex className="input-wrapper" vertical>
                  {/* 指令标签显示 */}
                  {tags && tags.length > 0 && (
                    <Flex className="instruction-header" align="center" justify="space-between">
                      <span className="instruction-title">{tags[0].title}</span>
                      <Button
                        type="text"
                        size="small"
                        icon={<CloseOutlined />}
                        onClick={tagClose}
                        className="instruction-close-btn"
                      />
                    </Flex>
                  )}

                  {/* 上传按钮 */}
                  <Upload
                    accept=".docx,.pptx,.xls,.xlsx,.csv,.txt,.pdf"
                    showUploadList={false}
                    beforeUpload={handleFileUpload}
                    multiple={true}
                    maxCount={uploadFileMaxCount()}
                  >
                    <Button
                      className="upload-button"
                      type="text"
                      icon={<UploadOutlined />}
                      style={{ position: "absolute", right: "0", top: "2px", zIndex: 10 }}
                      disabled={localFile.length >= 5}
                      title={localFile.length >= 5 ? "最多上传5个附件" : "点击上传文件"}
                    />
                  </Upload>

                  {/* 上传文件标签显示 */}
                  {localFile.length > 0 && (
                    <Flex className="file-tags-container" wrap="wrap" gap="small">
                      {localFile.map((file) => (
                        <Flex key={file.uid} className="file-tag-container" align="center" justify="space-between">
                          <div className="file-info">
                            <span className="file-icon">{getFileIcon(file.extension)}</span>
                            <span className="file-name" title={file.libName || file.name}>
                              {file.libName || file.name}
                            </span>
                          </div>
                          <Button
                            type="text"
                            size="small"
                            icon={<CloseOutlined />}
                            onClick={() => handleRemoveFile(file.uid)}
                            className="file-close-btn"
                            disabled={file.loading}
                          />
                          {file.loading && (
                            <div className="file-loading">
                              <Spin size="small" />
                            </div>
                          )}
                        </Flex>
                      ))}
                    </Flex>
                  )}

                  <Flex
                    className="mentions-container"
                    style={{
                      position: "relative",
                      paddingTop: localFile.length > 0 || tags.length > 0 ? 0 : 17,
                    }}
                  >
                    <Mentions
                      prefix="/"
                      autoFocus={true}
                      open={false}
                      className="text-input-mentions"
                      placeholder={tags && tags.length > 0 ? "直接输入想问的内容" : "请输入内容，输入【/】选择指令"}
                      rows={5}
                      value={query}
                      maxLength={10000}
                      onKeyDown={(e) => {
                        // 防止"/"键定位到网页输入框
                        if (e.key === "/") {
                          e.stopPropagation();
                          // 不再阻止默认行为，允许"/"输入到input框中
                          // e.preventDefault();
                          // 直接触发指令列表显示
                          setTimeout(() => {
                            fetchInstructionList();
                          }, 0);
                        }
                      }}
                      onInput={(e) => {
                        let value = (e.target as HTMLInputElement).value;

                        // 检查内容是否只包含空格或回车符
                        if (/^[\s]*$/.test(value)) {
                          setQuery(""); // 如果只包含空格或回车符，清空输入框
                        } else {
                          setQuery(value); // 否则更新输入内容
                        }

                        // 如果输入了"/"，则显示指令列表
                        if (value.includes("/") && value.trim().endsWith("/")) {
                          setTimeout(() => {
                            fetchInstructionList();
                          }, 0);
                        } else if (!value.includes("/")) {
                          setDropdownVisible(false);
                        }
                      }}
                      onSearch={(text) => {
                        // 当输入"/"时触发搜索
                        if (text === "/") {
                          // 使用setTimeout确保在DOM更新后再显示指令列表
                          setTimeout(() => {
                            fetchInstructionList();
                          }, 0);
                        }
                      }}
                      options={[]}
                    />
                  </Flex>
                </Flex>

                <div>
                  <div
                    className="chat-cue"
                    ref={popupContainerRef}
                    // style={{
                    //   display: promptList.length > 0 ? "flex" : "none",
                    // }}
                  ></div>
                </div>
              </Flex>
            </Flex>

            {/* 生成内容按钮 */}
            <Flex className="generate-button-container" justify="flex-end">
              <Dropdown
                menu={{
                  items: promptList.length
                    ? promptList.map((item) => ({ key: item.key, label: item.label }))
                    : [
                        {
                          key: "no-data",
                          label: (
                            <Empty
                              image={Empty.PRESENTED_IMAGE_SIMPLE}
                              description="暂无数据"
                              style={{ margin: 0, padding: "8px 12px" }}
                            />
                          ),
                          disabled: true,
                        },
                      ],
                  onClick: handlePromptClick,
                }}
                getPopupContainer={() => popupContainerRef.current} // 指定弹出层的容器
                trigger={["click"]}
                open={dropdownVisible}
                onOpenChange={(visible) => {
                  setDropdownVisible(visible);
                }}
              >
                <Button
                  className="cue-word-btn"
                  onClick={() => fetchInstructionList()}
                  icon={<IconFont type="PromptOutlined" className="icon" />}
                />
              </Dropdown>
              <Button
                className={`generate-button ${query.trim() ? "active" : ""}`}
                onClick={generateContent}
                disabled={!query.trim()}
              >
                <Flex align="center" justify="space-between" style={{ width: "100%" }}>
                  <span>生成内容</span>
                  {!!ocrPointsCost && ocrPointsCost > 0 && (
                    <Flex align="center">
                      &nbsp;&nbsp;
                      <span className="points-cost">{ocrPointsCost}&nbsp;</span>
                      <IconFont type="PointsCost" className="icon" />
                    </Flex>
                  )}
                </Flex>
              </Button>
            </Flex>

            {/* 结果区域 */}
            <Flex className="content-preview" vertical>
              <Flex className="result-header" vertical>
                <Divider className="title" orientation="left">
                  <Typography.Text className="title-text"> 结果</Typography.Text>
                </Divider>
              </Flex>

              {/* 停止生成按钮 */}
              {sseChat.progress != 0 && sseChat.isRendering == false && (
                <Flex className="right" onClick={handleStop}>
                  <Tooltip placement="top" title="停止生成">
                    <Button className="side-sub-btn-stop" icon={<PauseOutlined />}>
                      停止生成
                    </Button>
                  </Tooltip>
                </Flex>
              )}

              {/* AI生成结果显示 */}
              <Flex className="side-question-result" style={{ minHeight: contentHeight }}>
                <Flex className="side-question-result-content">
                  {sseChat.progress !== GenerationProgress.INITIALIZED && (
                    <AgentOutput
                      content={
                        sseChat.isRendering == false
                          ? sseChat.displayedText
                          : sseChat.chatList[0].length > 0
                            ? sseChat.chatList[0][1].content
                            : ""
                      }
                      finished={GenerationProgress.INITIALIZED == sseChat.progress || sseChat.isRendering}
                      role="assistant"
                    />
                  )}
                </Flex>

                {/* {sseChat.isRendering == true && (
                  <AgentOutput role="assistant" content={sseChat.displayedText} finished={true} />
                )} */}
              </Flex>

              {/* 复制按钮 */}
              {(sseChat.progress === GenerationProgress.RENDER_FINISHED ||
                sseChat.progress === GenerationProgress.USER_CANCELED) && (
                <Flex className="side-result-btn-icon">
                  <Flex vertical={false} className="side-result-btn-left">
                    <Tooltip
                      placement="top"
                      title={hasCopy ? "已复制" : "复制"}
                      getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                    >
                      <Button
                        onClick={handleCopyBtnClick}
                        icon={<CopyOutlined className=" btn-icon" />}
                        type="text"
                        size="small"
                      ></Button>
                    </Tooltip>
                    <Tooltip
                      placement="top"
                      title="重新生成"
                      getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                    >
                      <Button
                        onClick={handlerRefresh}
                        icon={<RedoOutlined className=" btn-icon" />}
                        type="text"
                        size="small"
                      ></Button>
                    </Tooltip>
                    <Tooltip
                      placement="top"
                      title="知识库"
                      getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                    >
                      <Button
                        icon={<IconFont type={"knowledgeBaseOutlined"} />}
                        style={{ height: "24px", width: "24px" }}
                        type="text"
                        onClick={(event) => {
                          event.stopPropagation();
                          setModalVisible(true);
                          setNoteInfoData({
                            title: sseChat.displayedText.slice(0, 15) + "...",
                            content: sseChat.displayedText,
                          });
                        }}
                      ></Button>
                    </Tooltip>
                  </Flex>
                </Flex>
              )}
            </Flex>
          </Flex>
        </Flex>
      </Flex>
      <NoteKnowledgeModal
        visible={modalVisible}
        noteInfo={noteInfoData}
        position="sider"
        onClose={() => {
          setModalVisible(false);
        }}
        onConfirm={() => {
          setModalVisible(false);
        }}
      />
    </>
  );
};

export default OCR;
