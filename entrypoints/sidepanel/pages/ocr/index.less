@import "@/assets/styles/variables";

.ocr-container {
  /* OCR容器 */
  height: 100%;
  width: 100%;

  position: relative;
  overflow-y: auto;
  scroll-behavior: smooth;

  padding-bottom: 20px; /* 添加底部填充，确保内容不被底部元素遮挡 */

  /* 蒙版 */

  .mask-box {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: lighten(@primary-color, 40%);
    border-radius: 20px;
    border: 1px dashed @primary-color;
    z-index: 1000;
    cursor: pointer;

    .mask-box-text-box {
      display: flex;
      justify-content: center;
      margin-top: 300px;

      .mask-box-img {
        width: 55px;
        height: 55px;
      }
    }

    .mask-box-text {
      font-size: 16px;
      color: @primary-color;
      font-family: @side-panel-font-family-bold;
      line-height: 22px;
      text-align: center;
      filter: blur(0px);
      margin-top: 40px;
    }
  }

  .ocr-uploader {
    height: 100%;
    .ocr-uploader-tip {
      user-select: none;
      font-size: var(--ant-font-size-lg);
      font-weight: var(--ant-font-weight-strong);
      margin: var(--ant-margin-sm) 0 var(--ant-margin-md);
      font-family: @side-panel-font-family-bold;
      text-align: center;
    }

    /* 截屏按钮样式 */
    .screenshot-button {
      width: 100%;
      margin-bottom: 16px;

      .screenshot-btn {
        width: 100%;
        background-color: #F1F3F6;
        border: 1px solid #DDDEDF;
      }
      .screenshot-btn:hover {
        .icon > path  {
          fill: var(--ant-color-primary-hover);
        }
      }
    }

    .ocr-uploader-icon {
      height: 24px;

      .btn-icon {
        min-width: 0px !important;
        width: 28px;
        height: 28px;
        border: none;
        background: transparent;
        .icon {
          color: var(--ant-color-text);
          font-size: var(--ant-font-size-lg);
        }
      }
    }

    .ocr-upload-box {
      width: 100%;
      height: 200px;
      min-height: 200px;
      border: none;
      backdrop-filter: blur(10px);
      margin-bottom: 10px;

      .ant-upload-wrapper {
        height: 100%;
      }

      .ant-upload-drag {

        border-radius: var(--ant-border-radius-lg);
        background: var(--ant-color-bg-layout); /* #F5F5F5 */
        border: 1px dashed #d9d9d9;
      }

      .ant-upload-drag:hover,
      .ant-upload-drag:focus {
        border: 1px dashed var(--ant-color-primary-hover);
        background: var(--ant-color-bg-base); /* #F5F5F5 */
        .ocr-upload-text,
        .upload-icon {
          color: var(--ant-color-primary-text-hover);
        }
      }

      .upload-icon {
        font-size: 20px;
        margin-bottom: 6px;
      }

      .ocr-upload-text {
        margin-top: var(--ant-margin-xs);
        user-select: none;

        font-size: var(--ant-font-size);
        color: var(--ant-color-text);
      }

      .ocr-upload-img {
        width: 24px;
        height: 24px;
      }
    }

    /* 提示文字 */
    .ocr-hint-text {
      margin: 0  0 10px 0;
      color: #787878;
      font-size: 14px;
    }

    /* 指令输入框 */
    .ocr-input-container {
      flex-direction: column;
      margin-bottom: 10px;

      .ocr-command-input {
        margin-bottom: 4px;
        border-radius: 4px;
      }

      .input-wrapper {
        width: 100%;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 4px 8px;
        .ant-upload-wrapper {
          height: 0;
        }
        .upload-button {
          font-size: 12px;
          color: rgba(0, 0, 0, 0.45);

          &:hover {
            color: var(--ant-color-primary);
          }
        }
        .input-with-tag {
          display: flex;
          align-items: center;
          width: 100%;

          .instruction-tag {
            background: #f7f7f7;
            color: rgba(0, 0, 0, 0.85);
            border-radius: 16px;
            margin-right: 8px;
            height: 28px;
            line-height: 28px;
            padding: 0 8px;
            white-space: nowrap;
          }
        }
      }

      .text-input-mentions {
        margin-bottom: 0;
        border: none;
        box-shadow: none !important;
        width: 100%;
        resize: none;
        padding: 4px 0; /* 恢复原来的padding */

        &:focus {
          outline: none !important;
          box-shadow: none !important;
          border: none !important;
        }
      }

      .ocr-input-hint {
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
        margin-top: 4px;
      }
    }

    /* 指令选择 */
    .prompt-select {
      margin-bottom: 16px;
      flex-wrap: wrap;
      gap: 8px;
    }

    /* 生成内容按钮 */
    .generate-button-container {
      gap: 8px;

      .cue-word-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        &:hover {
          border-color: var(--ant-color-primary);
          color: var(--ant-color-primary);
        }

        .icon {
          font-size: 16px;
        }
      }

      .generate-button {
        background-color: #F1F3F6;
        border: 1px solid #DDDEDF;

        .points-cost {
          color: var(--ant-color-primary);
        }
        &.active {
          background-color: var(--ant-color-primary);
          color: white;
          .points-cost {
            color: white;
          }
          svg {
            fill: white;
          }
        }

        .icon {
          font-size: 16px;
        }
      }
    }

    /* 指令下拉框 */
    .chat-cue {
      width: calc(100% - 2px);
      position: absolute;
      left: 1px;
      right: 1px;
      top: -180px; /* 与聊天页面保持一致 */
      z-index: 1000;
      display: block;
    }

    /* 指令下拉菜单样式 */
    .instruction-dropdown {
      z-index: 9999 !important;

      .ant-dropdown-menu {
        max-height: 300px;
        overflow-y: auto;
      }

      .ant-dropdown-menu-item {
        padding: 8px 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &:hover {
          background-color: var(--ant-color-primary-bg);
        }
      }
    }

    /* 确保下拉菜单在OCR页面内显示 */
    .chat-cue .ant-dropdown {
      width: 100%;
      position: absolute !important;
      left: 0px !important;
      top: 0px !important;
      height: 180px;
      z-index: 9999 !important;

      .ant-dropdown-menu {
        height: 100%;
      }

      .ant-dropdown-menu-item-group-title {
        padding: var(--ant-padding-xs);
      }

      .ant-dropdown-menu-item-group-list {
        margin: 0px !important;
        height: 134px !important;
        overflow-y: auto;
      }
    }

    .ant-mentions {
      width: 100%;
      resize: none;
      border-radius: 4px;

      &.text-input-mentions {

        min-height: 120px;
        textarea {
          padding: 0;
        }
        &:focus {
          border-color: var(--ant-color-primary);
          box-shadow: 0 0 0 2px rgba(var(--ant-color-primary-rgb), 0.2);
        }
      }
    }
  }

  .ocr-result {
    /* ocr结果 */
    border-radius: var(--ant-border-radius-lg);
    height: 100%;
    width: 100%;
    .image-preview {
      height: 170px;
      overflow: hidden;
      position: relative;
    }
    .upload-title {
      display: none;
      position: absolute;
      z-index: 99;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      font-size: var(--ant-font-size);
      color: var(--ant-color-text-light-solid);

      .upload-actions {
        position: absolute;
        bottom: 10px;
        right: 10px;
      }

      .action-icons {
        gap: 10px;
      }

      .icon {
        color: var(--ant-color-text-light-solid);
        font-size: var(--ant-font-size-lg);
      }

      .btn-icon {
        min-width: 0px !important;
        width: 22px;
        height: 22px;
        border: none;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        padding-bottom: 11px;
        .icon {
          font-size: var(--ant-font-size-sm);
        }
      }

      .btn-icon:hover {
        background: rgba(255, 255, 255, 0.3);
      }

      .delete-icon {
        margin-left: 2px;
      }
    }
    .ocr-upload-box:hover .upload-title {
      display: block;
    }

    .orc-text-screenshot {
      margin-top: var(--ant-margin-md);
      img {
        margin-right: 10px;
        width: 12px;
        height: 14px;
      }

      .orc-text-screenshot-text {
        width: 100%;
      }
    }
    .content-preview {
      /* OCR文本内容区 */

      width: 100%;
      background-color: var(--ant-color-bg-container);

      p {
        font-size: var(--ant-font-size);
        line-height: var(--ant-line-height);
      }

      .content-preview-wrapper {
        height: calc(100vh - 435px);
        padding: var(--ant-padding-sm) var(--ant-padding-sm) var(--ant-margin-xxl);
        padding-bottom: 30px;
        border: 1px solid var(--ant-color-border-secondary);
        border-radius: var(--ant-border-radius-lg);
      }

      .content-preview-text {
        height: 100%;
        overflow-y: auto;
      }
    }
    .operation {
      position: fixed;
      bottom: 12px;
    }
  }

  /* 结果区域样式 */
  .side-question-result {
    position: relative;
    padding-bottom: var(--ant-padding-xl);
    text-align: justify;
    min-height: 200px;
    padding-bottom: 60px;
    .side-question-result-content {
      height: 100%;
      padding-left: 6px;
      padding-bottom: 60px; /* 增加底部填充，确保内容不被按钮遮挡 */
    }
  }

  .side-result-btn-icon {
    width: 92%;
    position: fixed;
    bottom: 0;
    background-color: var(--ant-color-bg-container);
    height: 64px;
    z-index: 100;
    .side-result-icon-copy,
    .side-result-icon-afresh {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }

  .side-result-btn-left {
    position: fixed;
    bottom: var(--ant-margin-lg);
    left: var(--ant-margin-sm);
  }

  .btn-icon {
    color: var(--ant-color-text);
    font-size: var(--ant-font-size-heading-3);
  }

  /* 文件标签样式 - 从file-tag.less整合 */
  .file-tags-container {
    margin-bottom: 4px;
  }

  .file-tag-container {
    padding: 2px 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    width: calc(100% - 32px);
    position: relative;

    .file-info {
      display: flex;
      align-items: center;
      overflow: hidden;
      margin-right: 4px;

      .file-icon {
        margin-right: 4px;
        font-size: 16px;
      }

      .file-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 12px;
      }
    }

    .file-close-btn {
      padding: 0;
      height: 20px;
      width: 20px;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        font-size: 12px;
      }

      &:hover {
        color: var(--ant-color-primary);
        background-color: transparent;
      }
    }

    /* 文件上传loading样式 */
    .file-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      z-index: 1;
    }
  }

  /* 指令头部样式 - 从instruction-header.less整合 */
  .instruction-header {
    background-color: #F3F3F4;
    padding: 2px 8px;
    border-radius: 4px;
    margin-bottom: 4px;
    width: calc(100% - 32px);
    margin-bottom: 8px;

    .instruction-title {
      color: #989898;
      font-size: 12px;
    }

    .instruction-close-btn {
      padding: 0 4px;
      height: 20px;
      width: 20px !important;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        font-size: 12px;
        fill: #989898;
      }
      &:hover {
        color: var(--ant-color-primary);
        background-color: transparent;
      }
    }
  }

  /* Question页面样式 - 从question-styles.less整合 */
  .result {
    .result-header {
      position: relative;

      .title {
        margin-top: var(--ant-margin-sm);
        margin-bottom: var(--ant-margin-sm);

        .title-text {
          font-size: var(--ant-font-size-lg);
          color: var(--ant-color-text);
          font-weight: var(--ant-font-weight-strong);
        }
      }

      .agent-name {
        margin-bottom: var(--ant-margin-xs);
        img {
          width: 20px;
          height: 17.11px;
          vertical-align: text-top;
          padding-right: var(--ant-padding-xxs);
        }
      }
    }
  }

  /* Writer页面样式 - 从writer-styles.less整合 */
  .result-header {
    position: relative;
  }

  .right {
    z-index: 200;
    position: fixed;
    left: 50%;
    bottom: 10%;
    transform: translate(-50%);
    .side-sub-btn-stop {
      border: 1px solid var(--ant-color-border);
      border-radius: 50px;
    }
  }

  .side-write-result {
    overflow-y: auto;
    height: auto;
    margin-bottom: 40px;
  }

  /* 提交按钮样式 */
  .side-sub-btn {
    height: 40px;
    border-radius: 4px;
    font-size: var(--ant-font-size-lg);
  }
}
