export const defaultAgents: Agent[] = [
  {
    id: "0",
    agentName: "通用助手",
    appKey: import.meta.env["VITE_AI_CHAT_SECRET"],
    agentDesc:
      "## 欢迎使用AI办公助手\n\n你好，我是你的AI办公助手，很高兴能够为您提供帮助和支持。\n\n### 一些可能的问题：\n\n- 给我一段文本，让我提炼总结一下；\n- 提供场景给我，生成文件的大纲；\n- 给我一段需求和要求，生成一段代码。\n\n如果您有其他问题或需求，也欢迎随时向我提问。祝您使用愉快！",
  },
];

export const defaultPrompts: Prompt[] = [
  {
    id: "100180",
    content: "请将下列内容翻译成${lang}：\n${content}",
    agentName: "通用助手",
    appKey: import.meta.env["VITE_AI_CHAT_SECRET"],
    agentDesc: "",
    title: "翻译",
    default: true,
    isIns: true, // 是否是内置的
  },
  {
    id: "100175",
    content: "请对以下内容进行总结，不要超过200字：\n${content}",
    agentName: "通用助手",
    appKey: import.meta.env["VITE_AI_CHAT_SECRET"],
    title: "总结",
    agentDesc: "",
    default: true,
    isIns: true, // 是否是内置的
  },
  {
    id: "100179",
    content: "解释以下内容，不要超过200字：\n${content}",
    agentName: "通用助手",
    appKey: import.meta.env["VITE_AI_CHAT_SECRET"],
    title: "解释",
    agentDesc: "",
    default: true,
    isIns: true, // 是否是内置的
  },
  {
    id: "100177",
    content: "系统性地回答这个问题，给出你的思维链路，不要超过200字：\n${content}",
    agentName: "通用助手",
    appKey: import.meta.env["VITE_AI_CHAT_SECRET"],
    title: "回答问题",
    agentDesc: "",
    default: true,
    isIns: true, // 是否是内置的
  },
];
