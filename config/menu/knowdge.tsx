export const knowdgeSVGIcon = {
  // 知识库
  mainImage1: (
    <svg width="16px" height="16px" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-592.000000, -442.000000)">
          <g id="知识库" transform="translate(592.000000, 442.000000)">
            <path d="M0,0 L20,0 L20,20 L0,20 L0,0 Z" id="路径" fill="#FFFFFF" opacity="0"></path>
            <g id="Icon" transform="translate(3.333435, 2.500000)">
              <path
                d="M1.24761631,15 C0.55857718,15 0,14.4250663 0,13.7158489 L0,1.28415227 C0,0.57493493 0.558576584,0 1.24761571,0 L12.9190509,0 C13.60809,0 14.1666667,0.574934234 14.1666667,1.28415157 L14.1666667,13.7158489 C14.1666667,14.4250663 13.6080908,15 12.9190516,15 L1.24761631,15 Z"
                id="路径"
                className="stroke-color"
                strokeLinejoin="round"
                strokeDasharray="0,0"
                fillRule="nonzero"
              ></path>
              <line
                x1="5.33333333"
                y1="4.26666667"
                x2="8.66666667"
                y2="4.26666667"
                id="Vector"
                className="stroke-color"
                strokeLinejoin="round"
                strokeDasharray="0,0"
              ></line>
              <line
                x1="5.33333333"
                y1="7.6"
                x2="8.66666667"
                y2="7.6"
                id="Vector"
                className="stroke-color"
                strokeLinejoin="round"
                strokeDasharray="0,0"
              ></line>
              <line
                x1="5.33333333"
                y1="10.9333333"
                x2="8.66666667"
                y2="10.9333333"
                id="Vector"
                className="stroke-color"
                strokeLinejoin="round"
                strokeDasharray="0,0"
              ></line>
              <line
                x1="3.12906901"
                y1="0.19927969"
                x2="3.12906901"
                y2="14.7770515"
                id="路径"
                className="stroke-color"
                strokeDasharray="0,0"
              ></line>
              <polyline
                id="路径-3"
                className="stroke-color"
                strokeLinejoin="round"
                points="9.41656494 0 9.41656494 3 10.9165649 1.75 12.1665649 3 12.1665649 0.25"
              ></polyline>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),

  pdf: (
    <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-510.000000, -846.000000)">
          <g id="PPT" transform="translate(510.000000, 846.000000)">
            <rect id="矩形" fill="#FA541C" x="0" y="0" width="24" height="24" rx="3.2"></rect>
            <g id="file-excel-filled" transform="translate(7.000000, 6.000000)" fill="#FFFFFF" fillRule="nonzero">
              <g id="file-p-p-t-filled">
                <rect id="矩形" opacity="0" x="0" y="0" width="12" height="12"></rect>
                <path
                  d="M5.834,0 C8.351,0 10,1.60464 10,3.93352 C10,6.27232 8.348875,7.85852 5.825125,7.85852 L3.35525,7.85852 L3.35525,11.52 C3.35525,11.78508 3.13141667,12 2.85525,12 L1.5,12 C1.223875,12 1,11.78508 1,11.52 L1,0.48 C1,0.21492 1.223875,0 1.5,0 Z M5.53120833,1.92868 L3.35525,1.92868 L3.35525,5.94696 L4.80970833,5.94696 C6.802,5.94696 7.609125,5.42948 7.609125,3.93352 C7.609125,2.65172 6.85425,1.92868 5.53120833,1.92868 Z"
                  id="形状结合"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  excel: (
    <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-474.000000, -846.000000)">
          <g id="execl" transform="translate(474.000000, 846.000000)">
            <rect id="矩形" fill="#52C41A" x="0" y="0" width="24" height="24" rx="3.2"></rect>
            <g id="file-excel-filled" transform="translate(7.000000, 6.000000)" fill="#FFFFFF" fillRule="nonzero">
              <path
                d="M7.61206137,0.23356 L5.09141144,4.32584 L2.54510866,0.2318 C2.45564561,0.0879412159 2.29524257,0 2.1222278,0 L0.53953339,0 C0.446165354,0 0.354708305,0.0257000268 0.27575785,0.07412 C0.0450057235,0.21564 -0.0239646213,0.51208 0.121717217,0.73624 L3.51188761,5.95312 L0.0757644385,11.2646 C0.0262655059,11.3411082 0,11.4296359 0,11.52 C0,11.78508 0.221240396,12 0.494115903,12 L1.91329913,12 C2.08455774,12 2.24360211,11.9138461 2.33362706,11.77232 L4.91567089,7.71336 L7.48054419,11.77116 C7.57043284,11.9133729 7.72990653,12 7.90169564,12 L9.44523137,12 C9.53989582,12 9.63256642,11.973574 9.71221866,11.92388 C9.94185903,11.78068 10.0084823,11.48372 9.8610299,11.26068 L6.40917738,6.03864 L9.92135322,0.73984 C9.97270112,0.662356057 10,0.572151368 10,0.48 C10,0.21492 9.77880078,0 9.5058841,0 L8.03609517,0 C7.8623189,0 7.70132218,0.0886959134 7.61210255,0.23356 L7.61206137,0.23356 Z"
                id="路径"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  ppt: (
    <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
      <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-510.000000, -846.000000)">
          <g id="PPT" transform="translate(510.000000, 846.000000)">
            <rect id="矩形" fill="#FA541C" x="0" y="0" width="24" height="24" rx="3.2"></rect>
            <g id="file-excel-filled" transform="translate(7.000000, 6.000000)" fill="#FFFFFF" fillRule="nonzero">
              <g id="file-p-p-t-filled">
                <rect id="矩形" opacity="0" x="0" y="0" width="12" height="12"></rect>
                <path
                  d="M5.834,0 C8.351,0 10,1.60464 10,3.93352 C10,6.27232 8.348875,7.85852 5.825125,7.85852 L3.35525,7.85852 L3.35525,11.52 C3.35525,11.78508 3.13141667,12 2.85525,12 L1.5,12 C1.223875,12 1,11.78508 1,11.52 L1,0.48 C1,0.21492 1.223875,0 1.5,0 Z M5.53120833,1.92868 L3.35525,1.92868 L3.35525,5.94696 L4.80970833,5.94696 C6.802,5.94696 7.609125,5.42948 7.609125,3.93352 C7.609125,2.65172 6.85425,1.92868 5.53120833,1.92868 Z"
                  id="形状结合"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  txt: (
    <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-578.000000, -846.000000)">
          <g id="text-、" transform="translate(578.000000, 846.000000)">
            <rect id="矩形" fill="#722ED1" x="0" y="0" width="24" height="24" rx="3.2"></rect>
            <g id="file-word-filled" transform="translate(5.907404, 6.238281)" fill="#FFFFFF" fillRule="nonzero">
              <path
                d="M0,0.200000003 L0,2.8 C1.35270752e-17,2.91045695 0.0895430514,3 0.200000003,3 L1.3,3 C1.41045695,3 1.5,2.91045695 1.5,2.8 L1.5,1.5 L1.5,1.5 L5.25,1.5 L5.25,10.5 L3.2,10.5 C3.08954305,10.5 3,10.5895431 3,10.7 L3,11.8 C3,11.9104569 3.08954305,12 3.2,12 L8.8,12 C8.91045695,12 9,11.9104569 9,11.8 L9,10.7 C9,10.5895431 8.91045695,10.5 8.8,10.5 L6.75,10.5 L6.75,10.5 L6.75,1.5 L10.5,1.5 L10.5,2.8 C10.5,2.91045695 10.5895431,3 10.7,3 L11.8,3 C11.9104569,3 12,2.91045695 12,2.8 L12,0.200000003 C12,0.0895430514 11.9104569,-1.10320877e-16 11.8,0 L0.200000003,0 C0.0895430514,2.02906128e-17 -2.42335218e-16,0.0895430514 0,0.200000003 Z"
                id="路径"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  word: (
    <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <title>word</title>
      <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-546.000000, -846.000000)">
          <g id="word" transform="translate(546.000000, 846.000000)">
            <rect id="矩形" fill="#1677FF" x="0" y="0" width="24" height="24" rx="3.2"></rect>
            <g id="file-word-filled" transform="translate(5.860529, 7.531250)" fill="#FFFFFF" fillRule="nonzero">
              <path
                d="M6.00015068,3.1365 L7.73589661,9.70356667 C7.7821069,9.87841129 7.93832949,10 8.116805,10 L9.16126301,10 C9.33956138,10 9.49568551,9.87867701 9.5420728,9.70406667 L11.9864266,0.504066669 C11.9954388,0.470133149 12,0.435143028 12,0.400000002 C12,0.179100002 11.8234149,0 11.6056169,0 L10.4364023,0 C10.2526458,0 10.0932352,0.128702766 10.0520431,0.310333336 L8.54714273,6.94716667 L6.91176731,0.303133336 C6.86793434,0.125016488 6.71018612,0 6.5291828,0 L5.47118429,0 C5.29015646,0 5.13237295,0.124992374 5.08853405,0.303133336 L3.45644515,6.93403333 L1.94148804,0.309633336 C1.90003464,0.128341646 1.74078293,0 1.55729313,0 L0.394487305,0 C0.359917689,0 0.325497622,0.00460232197 0.292112015,0.0137000024 C0.0817743395,0.0710333357 -0.0429164636,0.290466669 0.0136117867,0.503800002 L2.45132685,9.7038 C2.49761471,9.87851615 2.65376337,10 2.83213664,10 L3.88349636,10 C4.06197187,10 4.21819446,9.87841129 4.26440474,9.70356667 L6.00015068,3.1365 Z"
                id="路径"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  hengMore: (
    <svg width="16px" height="16px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-623.000000, -544.000000)">
          <g id="3-Dot-Horizontal" transform="translate(623.000000, 544.000000)">
            <g id="Icon" transform="translate(2.291667, 8.333689)" stroke="#121212" strokeDasharray="0,0">
              <path
                d="M3.33333333,1.66666667 C3.33333333,2.58714125 2.58714125,3.33333333 1.66666667,3.33333333 C0.746192084,3.33333333 0,2.58714125 0,1.66666667 C0,0.746192084 0.746192084,0 1.66666667,0 C2.58714125,0 3.33333333,0.746192084 3.33333333,1.66666667 Z"
                id="路径"
              ></path>
              <path
                d="M9.375,1.66666667 C9.375,2.58714125 8.62880792,3.33333333 7.70833333,3.33333333 C6.78785875,3.33333333 6.04166667,2.58714125 6.04166667,1.66666667 C6.04166667,0.746192084 6.78785875,0 7.70833333,0 C8.62880792,0 9.375,0.746192084 9.375,1.66666667 Z"
                id="路径"
              ></path>
              <path
                d="M15.4166667,1.66666667 C15.4166667,2.58714125 14.6704746,3.33333333 13.75,3.33333333 C12.8295254,3.33333333 12.0833333,2.58714125 12.0833333,1.66666667 C12.0833333,0.746192084 12.8295254,0 13.75,0 C14.6704746,0 15.4166667,0.746192084 15.4166667,1.66666667 Z"
                id="路径"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
};
