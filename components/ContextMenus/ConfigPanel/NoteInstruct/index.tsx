// 旧版
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Button, Flex, Mentions, message, Tag, theme, Tooltip, Typography } from "antd";
import classNames from "classnames";
import { CopyIcon, GenerateIcon, NoteRegenerateIcon } from "@/config/menu/note";
import { langList } from "@/config/options/ai";
import { getUserInfo } from "@/utils/auth";
import AgentOutput from "@/components/AgentOutput";
import setModifyItem, { NOTE_MODIFY_STORAGE_KEY } from "@/utils/browserStorageCurrentPage";
import useSSEChat from "@/hooks/useSSEChat";
import ExpandableText from "@/components/ExpandableText/index";
import IconFont from "@/components/IconFont";
import { CloseOutlined, PauseCircleOutlined } from "@ant-design/icons";
import { SendByPrompt } from "@/types/chat";
import "./index.less";
import { defaultPrompts } from "@/config/agent";
import { pagePrompts } from "@/api/prompt.ts";
import { getToken } from "@/utils/auth.ts";

const { useToken } = theme;

const noteInstruct: React.FC<{
  note;
  themeBgcColor;
  isThumbnail: boolean;
  acitve: boolean;
  onExpanded?: () => void;
}> = ({ note, themeBgcColor, isThumbnail, acitve, onExpanded }) => {
  const { token } = useToken();
  // 输入框中的内容
  const [inputQuery, setInputQuery] = useState<string>("/");
  const [selectedPrompt, setSelectedPrompt] = useState({
    id: note.promptId,
    title: note.promptTitle,
    agentDesc: note.promptContent,
  });
  // 语言
  const [selectedLang, setSelectedLang] = useState<string>("0");
  // 是否已复制
  const [hasCopy, setHasCopy] = useState<boolean>(false);
  // 新结果
  const [newResult, setNewResult] = useState<boolean>(false);
  // 查询得到的提示词列表
  const [promptList, setPromptList] = useState<Prompt[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  //  结果div的ref
  const noteAiResultRef = useRef<HTMLDivElement>(null);
  const [resultList, setResultList] = useState<{ title: string; agentDesc: string }[]>([]);

  const fetchRequest = useFetchRequest();
  const sseChat = useSSEChat();

  useEffect(() => {
    if (note.content) {
      setResultList([{ title: selectedPrompt.title, agentDesc: note.content }]);
    }
  }, []);

  useEffect(() => {
    handlePromptSearch("", "init");
  }, []);

  // 消息列表变化时自动滚动到最底部
  useEffect(() => {
    if (noteAiResultRef.current) {
      noteAiResultRef.current.scrollTop = noteAiResultRef.current.scrollHeight - noteAiResultRef.current.clientHeight;
    }
  }, [sseChat.displayedText]);

  const sendByPrompt = (prompt: SendByPrompt) => {
    fetchRequest({
      api: "addNotePromptRela",
      params: prompt,
      callback: (res) => {
        if (res.code !== 200) {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  // 便签修改接口请求，重新存储 位置信息
  const editNoteRequest = (noteElement: CopilotNote) => {
    const note = noteElement;
    fetchRequest({
      api: "editNote",
      params: note,
      callback: (res) => {
        if (res.code === 200) {
          setModifyItem(NOTE_MODIFY_STORAGE_KEY, { ...note, key: new Date().getTime(), updateType: "edit" });
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };
  /** 提交并生成问题回复 */
  const handleSubmit = async (list?: Prompt[], item?) => {
    console.log(43423423);
    if (!inputQuery) {
      message.open({
        type: "error",
        content: "请选择提示词",
      });
      return;
    }
    if (!note.editable) {
      // 非本人创建
      return;
    }
    if (sseChat.progress === GenerationProgress.RESPONDING) {
      return;
    }
    let query = "";
    setSelectedPrompt;
    if (item || selectedPrompt.agentDesc) {
      let str = item?.agentDesc || selectedPrompt.agentDesc;
      str = str.replace("${lang}", langList[Number(selectedLang)].label);
      str = str.replace("${content}", note.quoteContent);
      query = str;
    } else {
      query = note.quoteContent;
    }
    setHasCopy(false);
    setNewResult(true);
    let selectPrompt = (list || promptList).find((x) => x.id === selectedPrompt.id);
    const appKey = selectPrompt?.appKey || import.meta.env["VITE_AI_CHAT_SECRET"];
    // 获取登录人信息
    const token = await getToken();
    getUserInfo().then((res) => {
      if (selectPrompt?.isIns) {
        sseChat.start({
          url: "/chat-messages",
          headers: {
            Authorization: `Bearer ${appKey}`,
            "Content-Type": "application/json",
            Token: token || "",
          },
          body: {
            conversation_id: "",
            inputs: {
              appKey: appKey, //agent
              query: { query },
            },
            response_mode: "streaming",
            user: res.id || "anonymous",
          },
          query: { query, noConversationId: "true" },
          onFinished: (result) => {
            setResultList(resultList.concat([{ title: selectedPrompt.title, agentDesc: result }]));
            sendByPrompt({
              noteId: note.id,
              promptId: selectedPrompt.id,
              promptTitle: selectedPrompt.title,
              promptContent: selectedPrompt.agentDesc,
            });
            editNoteRequest({ ...note, content: result, title: selectedPrompt.title });
          },
        });
      } else {
        sseChat.start({
          url: "/client/acct/ins/stream",
          headers: {
            Authorization: `Bearer ${appKey}`,
            "Content-Type": "application/json",
            Token: token || "",
          },
          body: {
            insId: selectPrompt.id,
            path: "/chat-messages",
            difyJson: {
              conversation_id: "",
              inputs: {
                appKey: appKey, //agent
                query: { query },
              },
              response_mode: "streaming",
              user: res.id || "anonymous",
              query: { query },
            },
          },
          query: { query, noConversationId: "true" },
          onFinished: (result) => {
            setResultList(resultList.concat([{ title: selectedPrompt.title, agentDesc: result }]));
            sendByPrompt({
              noteId: note.id,
              promptId: selectedPrompt.id,
              promptTitle: selectedPrompt.title,
              promptContent: selectedPrompt.agentDesc,
            });
            editNoteRequest({ ...note, content: result, title: selectedPrompt.title });
          },
        });
      }
    });
  };

  const handleStop = async () => {
    let selectPrompt = promptList.find((x) => x.id === selectedPrompt.id);
    const appKey = selectPrompt.appKey || import.meta.env["VITE_AI_CHAT_SECRET"];
    getUserInfo().then((res) => {
      sseChat.stop(appKey, res.id || "anonymous");
    });
  };

  /** 重新生成 */
  const handlerRefresh = () => {
    handleSubmit();
  };

  /** 处理点击复制按钮事件 */
  const handleCopyBtnClick = () => {
    let text = newResult ? sseChat.displayedText : note.content;
    copyText(text).then((res) => {
      res && setHasCopy(true);
    });
  };
  // 指令变更
  const inputChange = (e) => {
    if (selectedPrompt && selectedPrompt.title) return;
    setInputQuery(e.target.value);
  };

  /** 处理提示词搜索 */
  const handlePromptSearch = async (queryText: string, type?: string) => {
    // fetchRequest({
    //   api: "pagePrompts",
    //   params: {
    //     entity: {
    //       title: queryText,
    //       status: 0,
    //       // 查询所有可见Agent的提示词
    //       agentId: null,
    //       type: "prompt_type_created_coll",
    //     },
    //     pageNum: 1,
    //     pageSize: 100,
    //   },
    //   callback: (res) => {
    //     if (res.code === 200) {
    //       let list: Prompt[] = defaultPrompts
    //         .filter((item) => item.title.toLowerCase().includes(queryText.toLowerCase()))
    //         .concat(
    //           res.data?.records.map((x) => ({
    //             title: x.title,
    //             id: x.id,
    //             agentName: x.agentName,
    //             agentDesc: x.content,
    //             appKey: x.appKey,
    //           })) || [],
    //         );
    //       setPromptList(list);
    //       setSelectedPrompt(list.find((item) => item.id == selectedPrompt.id));
    //       setLoading(false);
    //       if (type === "init" && !isThumbnail && note && note.insertType === "add") {
    //         handleSubmit(list);
    //       }
    //     }
    //   },
    // });
    fetchRequest({
      api: "getAcctIns",
      params: {
        queryContent: "",
        insShowType: "1",
      },
      callback: (res) => {
        if (res.code === 200) {
          let arrShow: any = [];
          res.data.forEach((item) => {
            if (item.accountShowFlag !== 0) {
              arrShow.push(item);
            }
          });
          let list = defaultPrompts
            .filter((item) => item.title.toLowerCase().includes(queryText.toLowerCase()))
            .concat(
              arrShow.map((x) => ({
                title: x.name,
                id: x.id,
                agentName: x.agentName,
                agentDesc: x.tmplContent,
                appKey: x.appKey,
              })) || [],
            );
          setPromptList(list);
          const newPrompt = list.find((item) => item.id == selectedPrompt.id);
          setSelectedPrompt(newPrompt);
          setLoading(false);
          if (type === "init" && !isThumbnail && note && note.insertType === "add") {
            handleSubmit(list, newPrompt);
          }
        }
      },
    });
  };

  /** 防抖搜索提示词 */
  const debouncePromptSearch = useCallback(debounce(handlePromptSearch, 500), []);

  const [width, setWidth] = useState(isThumbnail ? 200 : 348); // 初始化宽度
  const [height, setHeight] = useState(240); // 初始化高度

  // 最大和最小尺寸限制
  const minWidth = 348;
  const minHeight = 240;
  const maxWidth = 800;
  const maxHeight = 600;

  // 鼠标摁下
  const onMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isThumbnail) return;
    // 记录鼠标按下时的位置以及当前盒子的尺寸
    const initialMouseX = e.clientX;
    const initialMouseY = e.clientY;
    const initialWidth = width;
    const initialHeight = height;
    const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
    let isDragging = true; // 是否正在拖拽
    const onMouseMove = (e) => {
      if (isDragging) {
        // 计算鼠标的移动距离
        const dx = e.clientX - initialMouseX;
        const dy = e.clientY - initialMouseY;

        // 计算新的宽度和高度，加入最大最小限制
        const newWidth = Math.max(minWidth, Math.min(initialWidth + dx, maxWidth));
        const newHeight = Math.max(minHeight, Math.min(initialHeight + dy, maxHeight));
        setWidth(newWidth); // 更新宽度
        setHeight(newHeight); // 更新高度
      }
    };

    const onMouseUp = () => {
      isDragging = false;
      document.removeEventListener("mousemove", onMouseMove);
      document.removeEventListener("mouseup", onMouseUp);
      shadowDom.removeEventListener("mouseup", onMouseUp);
    };
    document.addEventListener("mousemove", onMouseMove);
    document.addEventListener("mouseup", onMouseUp);
    shadowDom.addEventListener("mouseup", onMouseUp);
  };

  return (
    <Flex
      gap={token.paddingXS}
      vertical
      style={{
        borderRadius: token.borderRadiusSM,
        backgroundColor: note?.color,
        fontSize: token.fontSizeSM,
        height: `${isThumbnail ? "240px" : `${height}px`}`,
        width: `${width}px`,
        position: "relative",
        userSelect: "none",
      }}
      className={classNames("sino-tooltipInstance-sticky", isThumbnail ? "" : acitve ? "active" : "")}
    >
      <div
        onMouseDown={onMouseDown}
        style={{
          position: "absolute",
          right: 0,
          bottom: 0,
          width: "20px",
          height: "20px",
          cursor: "se-resize", // 鼠标样式为右下角调整大小
        }}
      />
      {/* 指令输入区 */}
      <Flex
        gap={token.paddingXS}
        style={{
          borderRadius: token.borderRadiusSM,
          backgroundColor: token.colorBgBase,
          height: "32px",
          padding: "0 12px 0 0",
          position: "relative",
          border: `1px solid ${token.controlItemBgActiveDisabled}`,
        }}
        id={`note-mentions-base-${note.id}`}
      >
        <Flex
          style={{
            position: "absolute",
            top: "4px",
            zIndex: 11,
            left: "6px",
            opacity: selectedPrompt?.title ? 1 : 0,
            maxWidth: "calc(100% - 40px)",
          }}
        >
          <Tag
            bordered={false}
            closable={!isThumbnail && note.editable}
            color="processing"
            className="sino-processing-tag"
            icon={<IconFont type="PromptOutlined" className="icon" fill="#1888FF" />}
            closeIcon={
              <div
                style={{ display: "inline-block" }}
                onClick={(e) => {
                  setSelectedPrompt({
                    id: "",
                    title: "",
                    agentDesc: "",
                  });
                  setInputQuery("");
                  e.stopPropagation();
                  e.preventDefault();
                }}
              >
                <CloseOutlined />
              </div>
            }
          >
            {selectedPrompt.title}
          </Tag>
        </Flex>
        <Mentions
          prefix="/"
          loading={loading}
          autoFocus={true}
          placement="top"
          className="sino-mentions-box"
          getPopupContainer={() => {
            const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
            return shadowDom.getElementById(`note-mentions-base-${note.id}`);
          }}
          style={{ background: token.colorBgBase, border: "none", boxShadow: "none" }}
          notFoundContent={<span className="placeholder">输入关键字来搜索提示词</span>}
          placeholder="输入【/】选择提示词"
          rows={1}
          value={inputQuery}
          disabled={sseChat.progress == GenerationProgress.RESPONDING}
          maxLength={100}
          onInput={inputChange}
          onKeyDown={(e) => {
            e.stopPropagation();
            if (e.key === "Enter" && e.ctrlKey && selectedPrompt.title) {
              handleSubmit();
            }
          }}
          onSearch={(text) => {
            setLoading(true);
            debouncePromptSearch(text);
          }}
          onSelect={(options) => {
            let selectPrompt = promptList.find((x) => x.id === options.key);
            setSelectedPrompt(selectPrompt);
          }}
          options={promptList.map((item) => {
            return {
              style: {
                display: "flex",
                gap: "8px",
                zIndex: 10061,
                background: themeBgcColor,
              },
              value: `${item.title}`,
              key: `${item.id}`,
              label: (
                <>
                  <Flex
                    align="center"
                    justify="space-between"
                    style={{
                      width: "100%",
                    }}
                  >
                    <Typography.Text className="prompt-item-txt">{item.title}</Typography.Text>
                    {!item.default && (
                      <Typography.Text
                        type="secondary"
                        style={{
                          fontSize: token.fontSizeSM,
                          maxWidth: "120px",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        @{item.agentName}
                      </Typography.Text>
                    )}
                  </Flex>
                </>
              ),
            };
          })}
        />
        {!isThumbnail && note.editable && (
          <Flex align="center" style={{ cursor: "pointer" }}>
            {[GenerationProgress.WAITING, GenerationProgress.RESPONDING].includes(sseChat.progress) ? (
              <PauseCircleOutlined style={{ fontSize: "16px", color: "#1888FF" }} onClick={handleStop} />
            ) : (
              <Flex
                onClick={() => {
                  handleSubmit();
                }}
              >
                <GenerateIcon />
              </Flex>
            )}
          </Flex>
        )}
      </Flex>
      {/* 引入内容区*/}
      <ExpandableText text={note.quoteContent} />
      {/* 指令输出区 */}
      <div className="side-panel-note-noteAiResult">
        <div
          className="side-panel-note-noteAiResult-result"
          style={{ maxHeight: isThumbnail ? "100px" : "auto" }}
          ref={noteAiResultRef}
        >
          {resultList.slice(0, resultList.length - 1).map((item, index) => {
            return (
              <Flex vertical style={{ marginBottom: "6px" }} key={index}>
                <Flex style={{ height: "22px" }} align="center">
                  <i
                    style={{
                      display: "inline-block",
                      width: "12px",
                      height: "12px",
                      margin: "4px",
                      borderRadius: "50%",
                      backgroundColor: token.colorBorder,
                    }}
                  ></i>
                  <Flex style={{ color: token.colorText, fontSize: token.fontSize }}>{item.title}</Flex>
                </Flex>
                <Flex style={{ borderLeft: "1px solid #ddd", marginLeft: "9px", marginBottom: "4px" }}>
                  <Flex
                    style={{
                      padding: "5px 0 5px 8px",
                    }}
                    className="sino-his-content"
                  >
                    <AgentOutput content={item.agentDesc} finished={true} role="assistant" />
                  </Flex>
                </Flex>
              </Flex>
            );
          })}
          {sseChat.displayedText || note.content ? (
            <div className="side-question-result">
              <AgentOutput
                content={newResult ? sseChat.displayedText : note.content}
                finished={
                  sseChat.progress === GenerationProgress.RENDER_FINISHED ||
                  sseChat.progress === GenerationProgress.INITIALIZED
                }
                role="assistant"
              />
            </div>
          ) : null}
        </div>

        <div className="side-panel-note-noteAiResult-title" style={{ background: note?.color }}>
          {isThumbnail ? (
            <Button color="primary" type="link" style={{ padding: 0 }} onClick={onExpanded}>
              展开便签
            </Button>
          ) : (
            <>
              {(sseChat.progress == GenerationProgress.RENDER_FINISHED ||
                (sseChat.progress == GenerationProgress.INITIALIZED && note.content)) && (
                <Flex gap={6}>
                  <Tooltip
                    placement="top"
                    title={hasCopy ? "已复制" : "复制"}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Button
                      icon={<CopyIcon width={14} height={14} />}
                      type="text"
                      style={{ padding: token.paddingXXS, height: "20px", width: "20px" }}
                      onClick={handleCopyBtnClick}
                    ></Button>
                  </Tooltip>
                  <Tooltip
                    placement="top"
                    title="重新生成"
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Button
                      icon={<NoteRegenerateIcon width={14} height={14} />}
                      type="text"
                      style={{ padding: token.paddingXXS, height: "20px", width: "20px" }}
                      onClick={handlerRefresh}
                    ></Button>
                  </Tooltip>
                </Flex>
              )}
              <div className="side-panel-note-noteAiResult-text">
                {[GenerationProgress.WAITING, GenerationProgress.RESPONDING].includes(sseChat.progress)
                  ? "生成中"
                  : "已生成" + (resultList.length > 1 ? "（" + resultList.length + "）" : "")}
              </div>
            </>
          )}
        </div>
      </div>
    </Flex>
  );
};

export default noteInstruct;
