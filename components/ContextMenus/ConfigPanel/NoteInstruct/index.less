.sino-mentions-box {
  .rc-textarea {
    background: none;
  }
}

.side-panel-note-noteAiResult {
  height: calc(100% - 100px);
  overflow-y: auto;
  .side-panel-note-noteAiResult-title {
    position: absolute;
    width: calc(100% - 32px);
    bottom: 12px;
    left: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 24px;
    .side-panel-note-noteAiResult-text {
      color: var(--ant-color-text-tertiary);
      font-size: var(--ant-font-size-sm);
    }
  }
  .side-panel-note-noteAiResult-result {
    color: rgba(18, 18, 18, 0.85);
    line-height: 20px;
    padding-bottom: 20px;
  }
}

.sino-processing-tag {
  display: flex;
  align-items: center;
  width: 100%;
  span {
    vertical-align: middle;
  }
  span:nth-child(2) {
    max-width: calc(100% - 30px);
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.sino-his-content * {
  color: var(--ant-color-text-tertiary) !important;
  font-size: var(--ant-font-size-sm) !important;
}
