// 新版
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Button, Flex, Input, Mentions, message, Modal, Space, Tag, theme, Tooltip, Typography } from "antd";
import classNames from "classnames";
import { AI, QUIZ } from "@/utils/notes";
import { CopyIcon, GenerateIcon, NoteRegenerateIcon } from "@/config/menu/note";
import { langList } from "@/config/options/ai";
import { getUserInfo } from "@/utils/auth";
import { createChatIcon } from "@/config/menu/chat";
import AgentOutput from "@/components/AgentOutput";
import setModifyItem, { NOTE_MODIFY_STORAGE_KEY } from "@/utils/browserStorageCurrentPage";
import useSSEChat from "@/hooks/useSSEChat";
import ExpandableText from "@/components/ExpandableText/index";
import IconFont from "@/components/IconFont";
import ItemLabel from "@/entrypoints/content/annotator/components/ItemLabel";
import { CloseOutlined, PauseCircleOutlined } from "@ant-design/icons";
import { SendByPrompt } from "@/types/chat";
import debounce from "lodash.debounce";
import "./index.less";
import { defaultPrompts } from "@/config/agent";
import { pagePrompts } from "@/api/prompt.ts";
import { getToken } from "@/utils/auth.ts";
import NoteKnowledgeModal from "@/components/NoteGroupModal/noteKnowledge";
const { useToken } = theme;

const noteInstruct: React.FC<{
  note;
  themeBgcColor;
  isThumbnail: boolean;
  acitve: boolean;
  onExpanded?: () => void;
}> = ({ note, themeBgcColor, isThumbnail, acitve, onExpanded }) => {
  const { token } = useToken();
  // 输入框中的内容
  const [inputQuery, setInputQuery] = useState<string>("");
  const [selectedPrompt, setSelectedPrompt] = useState({
    id: note.promptId,
    title: note.promptTitle,
    agentDesc: note.promptContent,
  });

  // 语言
  const [selectedLang, setSelectedLang] = useState<string>("0");
  // 是否已复制
  const [hasCopy, setHasCopy] = useState<boolean>(false);
  // 新结果
  const [newResult, setNewResult] = useState<boolean>(false);
  // 查询得到的提示词列表
  const [promptList, setPromptList] = useState<Prompt[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [isQue, setIsQue] = useState<boolean>(false);
  //  结果div的ref
  const noteAiResultRef = useRef<HTMLDivElement>(null);
  const [resultList, setResultList] = useState<{ title: string; agentDesc: string }[]>([]);
  const fetchRequest = useFetchRequest();
  const sseChat = useSSEChat();
  const [conversationId, setConversationId] = useState<string>("");
  const [modalVisible, setModalVisible] = useState(false); // 知识库弹框
  const [noteInfoData, setNoteInfoData] = useState<any>(); // 知识库弹框数据
  const [selected, setSelected] = useState<any>({});
  useEffect(() => {
    if (note.content) {
      setResultList([{ title: selectedPrompt?.title, agentDesc: note.content }]);
    }
  }, []);

  useEffect(() => {
    handlePromptSearch("", "init");
  }, []);
  // 消息列表变化时自动滚动到最底部
  useEffect(() => {
    if (noteAiResultRef.current) {
      noteAiResultRef.current.scrollTop = noteAiResultRef.current.scrollHeight - noteAiResultRef.current.clientHeight;
    }
  }, [sseChat.displayedText]);

  const sendByPrompt = (prompt: SendByPrompt) => {
    fetchRequest({
      api: "addNotePromptRela",
      params: prompt,
      callback: (res) => {
        if (res.code !== 200) {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  // 便签修改接口请求，重新存储 位置信息
  const editNoteRequest = (noteElement: CopilotNote) => {
    const note = noteElement;
    fetchRequest({
      api: "editNote",
      params: note,
      callback: (res) => {
        if (res.code === 200) {
          setModifyItem(NOTE_MODIFY_STORAGE_KEY, { ...note, key: new Date().getTime(), updateType: "edit" });
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };
  /** 提交并生成问题回复 */
  const handleSubmit = async (list?: Prompt[], item?) => {
    if (sseChat.progress === GenerationProgress.RESPONDING) {
      return;
    }
    setIsQue(true);
    let query = "";
    setSelectedPrompt;
    if (item || selectedPrompt?.agentDesc) {
      let str = item?.agentDesc || selectedPrompt?.agentDesc;
      str = str.replace("${lang}", langList[Number(selectedLang)].label);
      str = str.replace("${content}", note.quoteContent);
      query = str;
    } else {
      query = note.quoteContent;
    }
    if (inputQuery) {
      query = `${query}\n${inputQuery}`;
    }
    setHasCopy(false);
    setNewResult(true);
    let selectPrompt: any = {};
    if (selectedPrompt?.id) {
      selectPrompt = (list || promptList).find((x) => x.id === selectedPrompt.id);
    } else {
      selectPrompt.isIns = true;
    }
    const appKey = selectPrompt?.appKey || import.meta.env["VITE_AI_CHAT_SECRET"];
    const agentId = selectPrompt?.agentId || import.meta.env["VITE_AI_CHAT_AGENT_ID"];
    // 获取登录人信息
    const token = await getToken();
    const tenantId = await getTenantId();
    getUserInfo().then((res) => {
      if (selectPrompt?.isIns) {
        sseChat.start({
          url: "/dify/broker/agent/stream",
          headers: {
            // Authorization: `Bearer ${appKey}`,
            "Content-Type": "application/json",
            Token: token || "",
          },
          body: {
            insId: "1",
            bizType: "app:agent",
            bizId: agentId, // 取agentId
            agentId: agentId, // 取agentId
            path: "/chat-messages",
            difyJson: {
              conversation_id: "",
              inputs: {
                token: token || "",
                tenantid: tenantId || "",
                appKey: appKey, //agent
                query: { query },
              },
              response_mode: "streaming",
              user: res.id || "anonymous",
              query,
              noConversationId: "true",
            },
          },
          query: {
            // query, noConversationId: "true"
          },
          onFinished: (result) => {
            setConversationId(result[0]?.conversation_id);
            setResultList(resultList.concat([{ title: selectedPrompt?.title, agentDesc: result }]));
            sendByPrompt({
              noteId: note.id,
              promptId: selectedPrompt?.id,
              promptTitle: selectedPrompt?.title,
              promptContent: selectedPrompt?.agentDesc,
            });
            console.log(note.type, 909090114);
            if (note.type != QUIZ && note.type != AI) {
              editNoteRequest({ ...note, content: result, title: selectedPrompt?.title });
            }
          },
        });
      } else {
        sseChat.start({
          url: "/dify/broker/ins/stream",
          headers: {
            // Authorization: `Bearer ${appKey}`,
            "Content-Type": "application/json",
            Token: token || "",
          },
          body: {
            insId: "1",
            bizId: selectPrompt.id,
            path: "/chat-messages",
            bizType: "app:ins",
            agentId: agentId,
            difyJson: {
              conversation_id: "",
              inputs: {
                token: token || "",
                tenantid: tenantId || "",
                appKey: appKey, //agent
                query: { query },
              },
              response_mode: "streaming",
              user: res.id || "anonymous",
              query: { query },
            },
          },
          query: { query, noConversationId: "true" },
          onFinished: (result) => {
            setResultList(resultList.concat([{ title: selectedPrompt?.title, agentDesc: result }]));
            sendByPrompt({
              noteId: note.id,
              promptId: selectedPrompt.id,
              promptTitle: selectedPrompt?.title,
              promptContent: selectedPrompt.agentDesc,
            });
            if (note.type != QUIZ && note.type != AI) {
              editNoteRequest({ ...note, content: result, title: selectedPrompt?.title });
            }
          },
        });
      }
    });
  };

  const handleStop = async () => {
    let selectPrompt = promptList.find((x) => x.id === selectedPrompt.id);
    const appKey = selectPrompt.appKey || import.meta.env["VITE_AI_CHAT_SECRET"];
    const agentId = selectPrompt?.agentId || "0";
    getUserInfo().then((res) => {
      sseChat.stop(agentId, appKey, res.id || "anonymous");
    });
  };

  /** 重新生成 */
  const handlerRefresh = () => {
    handleSubmit();
  };

  /** 处理点击复制按钮事件 */
  const handleCopyBtnClick = () => {
    console.log("点击了复制");
    let text = newResult ? sseChat.displayedText : note.content;
    copyText(text).then((res) => {
      res && setHasCopy(true);
    });
    console.log(text);
  };
  // 指令变更
  const inputChange = (e) => {
    setInputQuery(e.target.value);
  };

  /** 处理提示词搜索 */
  const handlePromptSearch = async (queryText: string, type?: string) => {
    fetchRequest({
      api: "getAcctIns",
      params: {
        queryContent: "",
        insShowType: note.type == AI ? "1" : "2",
      },
      callback: (res) => {
        if (res.code === 200) {
          let arrShow: any = [];
          res.data.forEach((item) => {
            if (item.accountShowFlag !== 0) {
              item.title = item.name;
              item.agentDesc = item.tmplContent;
              arrShow.push(item);
            }
          });
          let list = defaultPrompts
            .filter((item) => item.title.toLowerCase().includes(queryText.toLowerCase()))
            .concat(
              arrShow.map((x) => ({
                title: x.name,
                id: x.id,
                agentName: x.agentName,
                agentDesc: x.tmplContent,
                appKey: x.appKey,
                agentId: x.agentId,
              })) || [],
            );
          if (note.type == AI) {
            setPromptList(list);
            const newPrompt = list.find((item) => item.id == selectedPrompt.id);
            setSelectedPrompt(newPrompt);
            setLoading(false);
            if (type === "init" && !isThumbnail && note && note.insertType === "add" && note.type != QUIZ) {
              handleSubmit(list, newPrompt);
            }
          } else {
            setPromptList(arrShow);
            const newPrompt = arrShow.find((item) => item.id == selectedPrompt.id);
            setSelectedPrompt(newPrompt);
            setLoading(false);
            if (type === "init" && !isThumbnail && note && note.insertType === "add" && note.type != QUIZ) {
              handleSubmit(arrShow, newPrompt);
            }
          }
        }
      },
    });
  };

  /** 防抖搜索提示词 */
  const debouncePromptSearch = useCallback(debounce(handlePromptSearch, 500), []);

  const [width, setWidth] = useState(isThumbnail ? 200 : 600); // 初始化宽度
  const [height, setHeight] = useState(85); // 初始化高度

  // 最大和最小尺寸限制
  const minWidth = 600;
  const minHeight = 340;
  const maxWidth = 800;
  const maxHeight = 600;

  // 鼠标摁下
  const onMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isThumbnail) return;
    // 记录鼠标按下时的位置以及当前盒子的尺寸
    const initialMouseX = e.clientX;
    const initialMouseY = e.clientY;
    const initialWidth = width;
    const initialHeight = height;
    const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
    let isDragging = true; // 是否正在拖拽
    const onMouseMove = (e) => {
      if (isDragging) {
        // 计算鼠标的移动距离
        const dx = e.clientX - initialMouseX;
        const dy = e.clientY - initialMouseY;

        // 计算新的宽度和高度，加入最大最小限制
        const newWidth = Math.max(minWidth, Math.min(initialWidth + dx, maxWidth));
        const newHeight = Math.max(minHeight, Math.min(initialHeight + dy, maxHeight));
        setWidth(newWidth); // 更新宽度
        setHeight(newHeight); // 更新高度
      }
    };

    const onMouseUp = () => {
      isDragging = false;
      document.removeEventListener("mousemove", onMouseMove);
      document.removeEventListener("mouseup", onMouseUp);
      shadowDom.removeEventListener("mouseup", onMouseUp);
    };
    document.addEventListener("mousemove", onMouseMove);
    document.addEventListener("mouseup", onMouseUp);
    shadowDom.addEventListener("mouseup", onMouseUp);
  };
  // 指令点击
  const handleClickItem = (key, lang?: string) => {
    console.log(key, 243);
    // let obj = records.current.find((x) => `${x.id}` === `${key}`);
    // if (key === "100180" && lang) {
    //   obj.content = obj.content.replace("${lang}", lang);
    // }
  };

  const handleClose = () => {
    // 获取祖元素 class 为 "sino-customize-tooltip" 的 ID
    window.postMessage({ type: "delNoteNotice", note }, "*");
  };
  // 保存为便签
  const addNote = debounce(() => {
    let obj = JSON.parse(JSON.stringify(note));
    obj.title = note.quoteContent;
    obj.content = newResult ? sseChat.displayedText : note.content;

    // obj.insertType = "add";
    // obj.type = "NOTE";
    // obj.color = note.color;
    // obj.title = note.title;
    // obj.agent = selectedPrompt.agentName;
    // obj.info = selectedPrompt.agentDesc;
    // obj.promptId = selectedPrompt.id;
    obj.type = "ONQUIZ";

    window.postMessage(
      {
        type: "addNoteNotice",
        note: obj,
        target: "",
        info: "",
        agent: "",
      },
      "*",
    );
  }, 2000);
  // 继续问答
  const continueQ = () => {
    const agentId = selected?.agentId || "0";
    browser.runtime.sendMessage({
      type: "openChat",
      data: { currentConversation: sseChat.currentConversation, agentId: agentId },
    });
  };
  return (
    <Flex
      gap={token.padding}
      vertical
      style={{
        borderRadius: token.borderRadiusSM,
        backgroundColor: note?.color,
        fontSize: token.fontSizeSM,
        minHeight: `${isThumbnail ? "240px" : `${height}px`}`,
        maxHeight: `${maxHeight}px`,
        width: `${width}px`,
        position: "relative",
        userSelect: "none",
      }}
      className={classNames("sino-tooltipInstance-sticky", isThumbnail ? "" : acitve ? "active" : "")}
    >
      <Flex style={{ position: "absolute", right: "15px", top: "15px", cursor: "pointer", fontSize: token.fontSizeLG }}>
        <CloseOutlined style={{ color: token.colorTextQuaternary }} onClick={handleClose} />
      </Flex>
      <div
        onMouseDown={onMouseDown}
        style={{
          position: "absolute",
          right: 0,
          bottom: 0,
          width: "20px",
          height: "20px",
          cursor: "se-resize", // 鼠标样式为右下角调整大小
        }}
      />
      {/* 指令展示 */}
      {selectedPrompt?.title && (
        <Tag
          bordered={false}
          color="processing"
          className="note-ins-processing-tag"
          icon={<IconFont type="PromptOutlined" className="icon" fill="#1888FF" />}
          closeIcon={
            <div
              style={{ display: "inline-block" }}
              onClick={(e) => {
                setSelectedPrompt({
                  id: "",
                  title: "",
                  agentDesc: "",
                });
                setInputQuery("");
                e.stopPropagation();
                e.preventDefault();
              }}
            >
              <CloseOutlined />
            </div>
          }
        >
          {selectedPrompt?.title}
        </Tag>
      )}
      {/* 引入内容区*/}
      <Flex style={{ width: "calc(100% - 30px)" }}>
        <ExpandableText text={note.quoteContent} />
      </Flex>
      {/* 指令输出区 */}
      {isQue && (
        <Flex className="note-ins-note-noteAiResult" vertical>
          <div
            className="side-panel-note-noteAiResult-result"
            style={{ maxHeight: isThumbnail ? "100px" : "auto" }}
            ref={noteAiResultRef}
          >
            {resultList.slice(0, resultList.length - 1).map((item, index) => {
              return (
                <Flex vertical style={{ marginBottom: "6px" }} key={index}>
                  <Flex style={{ height: "22px" }} align="center">
                    <i
                      style={{
                        display: "inline-block",
                        width: "12px",
                        height: "12px",
                        margin: "4px",
                        borderRadius: "50%",
                        backgroundColor: token.colorBorder,
                      }}
                    ></i>
                    <Flex style={{ color: token.colorText, fontSize: token.fontSize }}>{item.title}</Flex>
                  </Flex>
                  <Flex style={{ borderLeft: "1px solid #ddd", marginLeft: "9px", marginBottom: "4px" }}>
                    <Flex
                      style={{
                        padding: "5px 0 5px 8px",
                      }}
                      className="sino-his-content"
                    >
                      <AgentOutput content={item.agentDesc} finished={true} role="assistant" />
                    </Flex>
                  </Flex>
                </Flex>
              );
            })}
            {sseChat.displayedText || note.content ? (
              <div className="side-question-result">
                <AgentOutput
                  content={newResult ? sseChat.displayedText : note.content}
                  finished={
                    sseChat.progress === GenerationProgress.RENDER_FINISHED ||
                    sseChat.progress === GenerationProgress.INITIALIZED
                  }
                  role="assistant"
                />
              </div>
            ) : null}
          </div>
          {sseChat.isRendering && (
            <div className="side-panel-note-noteAiResult-title" style={{ background: note?.color }}>
              {isThumbnail ? (
                <Button color="primary" type="link" style={{ padding: 0 }} onClick={onExpanded}>
                  展开便签
                </Button>
              ) : (
                <>
                  {(sseChat.progress == GenerationProgress.RENDER_FINISHED ||
                    (sseChat.progress == GenerationProgress.INITIALIZED && note.content)) && (
                    <Flex>
                      <Flex
                        align="center"
                        onClick={addNote}
                        style={{
                          borderRight: "1px solid rgba(0, 0, 0, 0.06)",
                          paddingRight: token.paddingXS,
                          marginRight: token.marginXXS,
                          cursor: "pointer",
                        }}
                      >
                        <Button
                          icon={<IconFont type={"NoteOutlined"} style={{ fill: token.colorTextTertiary }} />}
                          type="text"
                          style={{ padding: token.paddingXXS, height: "20px", width: "20px" }}
                        />
                        <Flex style={{ color: token.colorText, marginLeft: token.marginXXS }}>保存为便签</Flex>
                      </Flex>
                      <Flex gap={6}>
                        <Tooltip
                          placement="top"
                          title={hasCopy ? "已复制" : "复制"}
                          getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                        >
                          <Button
                            icon={<CopyIcon width={14} height={14} />}
                            type="text"
                            style={{ padding: token.paddingXXS, height: "20px", width: "20px" }}
                            onClick={handleCopyBtnClick}
                          ></Button>
                        </Tooltip>
                        <Tooltip
                          placement="top"
                          title="重新生成"
                          getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                        >
                          <Button
                            icon={<NoteRegenerateIcon width={14} height={14} />}
                            type="text"
                            style={{ padding: token.paddingXXS, height: "20px", width: "20px" }}
                            onClick={handlerRefresh}
                          ></Button>
                        </Tooltip>
                        <Tooltip
                          placement="top"
                          title="知识库"
                          getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                        >
                          <Button
                            icon={<IconFont type={"knowledgeBaseOutlined"} style={{ fill: token.colorTextTertiary }} />}
                            type="text"
                            style={{ padding: token.paddingXXS, height: "20px", width: "20px" }}
                            onClick={() => {
                              console.log(sseChat.displayedText);
                              setNoteInfoData({
                                title: note.quoteContent,
                                content: sseChat.displayedText,
                              });
                              setModalVisible(true);
                            }}
                          ></Button>
                        </Tooltip>
                      </Flex>
                    </Flex>
                  )}
                </>
              )}
            </div>
          )}
        </Flex>
      )}

      {note.type == QUIZ && !isQue && (
        <Flex
          gap={token.paddingXS}
          style={{
            borderRadius: token.borderRadiusSM,
            backgroundColor: token.colorBgBase,
            height: "32px",
            padding: "0 12px 0 0",
          }}
          id={`note-mentions-base-${note.id}`}
        >
          <Mentions
            prefix="/"
            loading={loading}
            autoFocus={true}
            placement="top"
            className="sino-mentions-box"
            getPopupContainer={() => {
              const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
              return shadowDom.getElementById(`note-mentions-base-${note.id}`);
            }}
            style={{ background: token.colorBgBase, border: "none", boxShadow: "none" }}
            notFoundContent={<span className="placeholder">输入关键字来搜索提示词</span>}
            placeholder="输入【/】选择提示词"
            rows={1}
            value={inputQuery}
            disabled={sseChat.progress == GenerationProgress.RESPONDING}
            maxLength={100}
            onInput={inputChange}
            onKeyDown={(e) => {
              e.stopPropagation();
              if (e.key === "Enter" && e.ctrlKey && selectedPrompt?.title) {
                handleSubmit();
              }
            }}
            onSearch={(text) => {
              setLoading(true);
              debouncePromptSearch(text);
            }}
            onSelect={(options) => {
              let selectPrompt = promptList.find((x) => x.id === options.key);
              setSelectedPrompt(selectPrompt);
            }}
            options={promptList.map((item) => ({
              key: `${item.id}`,
              value: `${item.title}`,
              label: <ItemLabel item={item} exceedHalfViewport={false} handleClickItem={handleClickItem} />, // 传递自定义组件
            }))}
          />
          {!isThumbnail && (
            <Flex
              align="center"
              style={{ cursor: "pointer" }}
              onClick={() => {
                handleSubmit();
              }}
            >
              {!inputQuery ? createChatIcon.unselected : createChatIcon.selected}
            </Flex>

            // <Button
            //   disabled={!inputQuery}
            //   type="link"
            //   className={classNames("send-btn", inputQuery.trim() && "active")}
            //   onClick={() => handleSubmit()}
            // >

            // </Button>
          )}
        </Flex>
      )}
      <Flex
        style={{
          width: "calc(100% - 52px)",
          paddingLeft: token.padding,
          fontSize: token.fontSize,
          position: "absolute",
          left: "16px",
          bottom: "16px",
        }}
      >
        {!sseChat.isRendering && isQue && (
          <Flex align="center" justify="space-between" style={{ flex: 1 }}>
            <Flex>
              <IconFont className="icon" type="AIChatOutlined" fill={token.colorTextDisabled} />
              <Flex style={{ marginLeft: token.marginXS, color: token.colorTextDisabled, fontSize: token.fontSize }}>
                继续问答
              </Flex>
            </Flex>
            <PauseCircleOutlined style={{ fontSize: "16px", color: "#1888FF" }} onClick={handleStop} />
          </Flex>
        )}
        {sseChat.isRendering && (
          <Flex>
            <Flex onClick={continueQ} style={{ cursor: "pointer" }} align="center">
              <IconFont className="icon" type="AIChatOutlined" fill={token.colorInfoText} />
              <Flex style={{ marginLeft: token.marginXS, color: token.colorText, fontSize: token.fontSize }}>
                继续问答
              </Flex>
            </Flex>
          </Flex>
        )}
      </Flex>
      <NoteKnowledgeModal
        visible={modalVisible}
        noteInfo={noteInfoData}
        onClose={() => {
          setModalVisible(false);
        }}
        onConfirm={() => {
          setModalVisible(false);
        }}
      />
    </Flex>
  );
};

export default noteInstruct;
