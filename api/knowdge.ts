import request from "@/api";

const API_BASE_PUB: string = import.meta.env["VITE_API_BASE_PUB"] || "";
const API_BASE_AI: string = import.meta.env["VITE_API_BASE_AI"] || "";
const API_BASE_DOC: string = import.meta.env["VITE_API_BASE_DOC"] || "";

export function downLoadFile(query) {
  return request({
    url: `${API_BASE_PUB}/pub/oss/download`,
    method: "GET",
    responseType: "blob",
    params: query,
  });
}

// 知识库列表
export function getAllData(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/base/page`,
    method: "POST",
    data: query,
  });
}
// 获取知识库列表个人库
export function getKnowledgeTeam(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/base/team/page`,
    method: "POST",
    data: query,
  });
}

// 获取知识库列表企业库加个人库
export function getKnowledgeData(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/base/page`,
    method: "POST",
    data: query,
  });
}

export function getItemDetail(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/doc/team/page`,
    method: "POST",
    data: query,
  });
}

export function addKnowledge(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/base/team/add`,
    method: "POST",
    data: query,
  });
}
// 团队库
export function editKnowledge(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/base/team/edit`,
    method: "POST",
    data: query,
  });
}
// 企业库

export function editEnterprise(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/base/enterprise/edit`,
    method: "POST",
    data: query,
  });
}

export function uploadKnowledgeFile(data) {
  return request({
    url: `${API_BASE_DOC}/knowledge/doc/teamUpload`,
    method: "POST",
    data: data,
    status: "1",
  });
}

// 删除文件夹
export function delKnowledge(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/base/team/del`,
    method: "DELETE",
    params: query,
  });
}

// 删除文件
export function delFile(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/doc/del`,
    method: "DELETE",
    params: query,
  });
}

// agent列表
export function listAgents(query) {
  return request({
    url: `${API_BASE_AI}/agent/baseInfo/list`,
    method: "POST",
    data: query,
  });
}

export function businessUpdateTmpFileConversationRela(query) {
  return request({
    url: `${API_BASE_AI}/knowledge/business/updateTmpFileConversationRela`,
    method: "POST",
    data: query,
  });
}

export function libBaseInfoAddUrl(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/base/addUrl`,
    method: "POST",
    data: query,
  });
}

export function libBaseInfoGetUrl(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/base/getUrl`,
    method: "GET",
    params: query,
  });
}

export function libBaseInfoDelUrl(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/base/delUrl`,
    method: "GET",
    params: query,
  });
}

// 获取个人库列表
export function getTeamList(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/base/team/list`,
    method: "POST",
    data: query,
  });
}

// 存入个人库
export function addNoteKnowledge(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/doc/note/add`,
    method: "POST",
    data: query,
  });
}

// 删除企业库
export function delEnterpriseKnowledge(query: any): Promise<any> {
  return request({
    url: `${API_BASE_DOC}/knowledge/base/enterprise/del`,
    method: "DELETE",
    params: query,
  });
}

// 分页查询黑名单（用户端）
export function blackUrlClientPage(data): Promise<any> {
  return request({
    url: `${API_BASE_DOC}/knowledge/blackUrl/clientPage`,
    method: "POST",
    data,
  });
}

// 删除黑名单（用户端）
export function delBlackUrl(query: any): Promise<any> {
  return request({
    url: `${API_BASE_DOC}/knowledge/blackUrl/clientDel`,
    method: "DELETE",
    params: query,
  });
}

// url默认入库（客户端）
export function clientCrawSave(data: any): Promise<any> {
  return request({
    url: `${API_BASE_DOC}/knowledge/doc/clientCrawSave`,
    method: "POST",
    data,
  });
}

// 新增黑名单（用户端）
export function addClient(data: any): Promise<any> {
  return request({
    url: `${API_BASE_DOC}/knowledge/blackUrl/clientAdd`,
    method: "POST",
    data,
  });
}

// 网页存入知识库配置详情
export function settingInfo(query) {
  return request({
    url: `${API_BASE_DOC}/knowledge/setting/info`,
    method: "GET",
    params: query,
  });
}

// 网页存入知识库配置 修改
export function editSetting(data) {
  return request({
    url: `${API_BASE_DOC}/knowledge/setting/edit`,
    method: "POST",
    data,
  });
}

// 个人库分享
export function memberShare(data) {
  return request({
    url: `${API_BASE_DOC}/knowledge/member/share`,
    method: "POST",
    data,
  });
}

// 查询分享成员列表
export function memberList(query: any) {
  return request({
    url: `${API_BASE_DOC}/knowledge/member/list`,
    method: "GET",
    params: query,
  });
}

// 团队库设置权限范围
export function changePermission(query: any) {
  return request({
    url: `${API_BASE_DOC}/knowledge/member/changePermission`,
    method: "GET",
    params: query,
  });
}

// orc 文件链接入知识库
export function ocrAdd(data: any) {
  return request({
    url: `${API_BASE_DOC}/knowledge/doc/ocr/add`,
    method: "POST",
    data,
  });
}
